import {
  createParamDecorator,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { RequestWithUser } from '../middlewares/tenant-security.middleware';
import { AppException, ErrorCode } from '@/common';

/**
 * Decorator để lấy tenantId từ request
 * Sử dụng trong controller để lấy tenantId của người dùng hiện tại
 *
 * @example
 * @Get()
 * findAll(@CurrentTenant() tenantId: number) {
 *   return this.service.findAll(tenantId);
 * }
 */
export const CurrentTenant = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): number => {
    const request = ctx.switchToHttp().getRequest<RequestWithUser>();

    // Lấy tenantId từ request
    let tenantId = request.tenantId;

    console.log(`[CurrentTenant] Request tenantId: ${tenantId}, User tenantId: ${request.user?.tenantId}`);

    // Nếu không có trong request, l<PERSON><PERSON> từ user
    if (!tenantId && request.user?.tenantId) {
      tenantId = typeof request.user.tenantId === 'string'
        ? parseInt(request.user.tenantId, 10)
        : request.user.tenantId;
      console.log(`[CurrentTenant] Using tenantId from user: ${tenantId}`);
    }

    if (!tenantId) {
      console.error(`[CurrentTenant] No tenantId found in request or user`);
      throw new AppException(
        ErrorCode.UNAUTHORIZED_ACCESS,
        'TenantId không tồn tại trong request. Đảm bảo rằng TenantSecurityMiddleware đã được áp dụng.',
      );
    }

    console.log(`[CurrentTenant] Returning tenantId: ${tenantId}`);
    return tenantId;
  },
);
