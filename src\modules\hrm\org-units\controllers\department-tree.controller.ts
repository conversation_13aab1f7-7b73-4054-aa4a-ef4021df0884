import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DepartmentTreeService } from '../services/department-tree.service';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { DepartmentTreeResponseDto } from '../dto/department/department-tree.dto';

/**
 * Controller quản lý cấu trúc cây phòng ban
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('api/hrm/departments/tree')
export class DepartmentTreeController {
  constructor(private readonly departmentTreeService: DepartmentTreeService) {}

  /**
   * Lấy cấu trúc cây phòng ban
   * @param user Người dùng hiện tại
   * @returns Cấu trúc cây phòng ban dạng kim tự tháp
   */
  @Get()
  @ApiOperation({ summary: 'Lấy cấu trúc cây phòng ban' })
  @ApiResponse({
    status: 200,
    description: 'Cấu trúc cây phòng ban dạng kim tự tháp.',
    type: () => ApiResponseDto,
  })
  async getDepartmentTree(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DepartmentTreeResponseDto>> {
    const result = await this.departmentTreeService.getDepartmentTree(Number(user.tenantId));
    return ApiResponseDto.success(result);
  }
}
