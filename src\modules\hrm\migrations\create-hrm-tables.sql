-- Tạo enum types trước khi tạo bảng
DO $$
BEGIN
    -- Tạo enum cho employee_status
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'employee_status_enum') THEN
        CREATE TYPE employee_status_enum AS ENUM (
            'active', 'inactive', 'on_leave', 'terminated', 'probation', 'suspended'
        );
    END IF;

    -- Tạo enum cho employment_type
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'employment_type_enum') THEN
        CREATE TYPE employment_type_enum AS ENUM (
            'full_time', 'part_time', 'contract', 'temporary', 'intern', 'freelance'
        );
    END IF;

    -- Tạo enum cho marital_status
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'marital_status_enum') THEN
        CREATE TYPE marital_status_enum AS ENUM (
            'single', 'married', 'divorced', 'widowed', 'separated'
        );
    END IF;

    -- Tạo enum cho contract_status
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'contract_status_enum') THEN
        CREATE TYPE contract_status_enum AS ENUM (
            'draft', 'pending', 'active', 'expired', 'terminated', 'renewed'
        );
    END IF;

    -- Tạo enum cho contract_type
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'contract_type_enum') THEN
        CREATE TYPE contract_type_enum AS ENUM (
            'probation', 'definite', 'indefinite', 'part_time', 'internship', 'freelance'
        );
    END IF;
END$$;



-- Tạo bảng employees với enum types
DROP TABLE IF EXISTS employees CASCADE;
CREATE TABLE employees (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    employee_code VARCHAR(50) NOT NULL,
    employee_name VARCHAR(255) NOT NULL,
    department_id INTEGER,
    job_title VARCHAR(255),
    job_level VARCHAR(50),
    manager_id INTEGER,
    employment_type employment_type_enum,
    status employee_status_enum NOT NULL DEFAULT 'active',
    hire_date DATE,
    termination_date DATE,
    termination_reason VARCHAR(500),
    probation_end_date DATE,
    marital_status marital_status_enum,
    number_of_dependents INTEGER,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    notes TEXT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by INTEGER,
    updated_by INTEGER,
    tenant_id INTEGER,
    CONSTRAINT employees_user_id_fk FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT employees_department_id_fk FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    CONSTRAINT employees_manager_id_fk FOREIGN KEY (manager_id) REFERENCES employees(id) ON DELETE SET NULL,
    CONSTRAINT employees_employee_code_unique UNIQUE (employee_code, tenant_id)
);

-- Tạo bảng contracts với enum types
DROP TABLE IF EXISTS contracts CASCADE;
CREATE TABLE contracts (
    id SERIAL PRIMARY KEY,
    contract_code VARCHAR(50) NOT NULL,
    employee_id INTEGER NOT NULL,
    contract_type contract_type_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    signing_date DATE,
    status contract_status_enum NOT NULL DEFAULT 'draft',
    base_salary DECIMAL(15, 2) NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'VND',
    working_hours_per_week INTEGER,
    probation_period_days INTEGER,
    notice_period_days INTEGER,
    termination_date DATE,
    termination_reason VARCHAR(500),
    document_path VARCHAR(500),
    notes TEXT,
    created_at BIGINT,
    updated_at BIGINT,
    created_by INTEGER,
    updated_by INTEGER,
    tenant_id INTEGER,
    CONSTRAINT contracts_employee_id_fk FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    CONSTRAINT contracts_contract_code_unique UNIQUE (contract_code, tenant_id)
);

-- Thêm comment cho các bảng và cột
COMMENT ON TABLE employees IS 'Bảng lưu trữ thông tin nhân viên';
COMMENT ON COLUMN employees.employee_code IS 'Mã nhân viên, duy nhất trong hệ thống';
COMMENT ON COLUMN employees.employee_name IS 'Tên nhân viên, được sử dụng để hiển thị trong hệ thống';
COMMENT ON COLUMN employees.status IS 'Trạng thái nhân viên: active, inactive, on_leave, terminated, probation, suspended';
COMMENT ON COLUMN employees.employment_type IS 'Loại hợp đồng: full_time, part_time, contract, temporary, intern, freelance';

COMMENT ON TABLE contracts IS 'Bảng lưu trữ thông tin hợp đồng';
COMMENT ON COLUMN contracts.contract_code IS 'Mã hợp đồng, duy nhất trong hệ thống';
COMMENT ON COLUMN contracts.status IS 'Trạng thái hợp đồng: draft, pending, active, expired, terminated, renewed';
COMMENT ON COLUMN contracts.contract_type IS 'Loại hợp đồng: probation, definite, indefinite, part_time, internship, freelance';
