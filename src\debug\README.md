# Tenant Debug Tools

Bộ công cụ debug để kiểm tra và phân tích hệ thống tenant isolation trong ứng dụng.

## 🚀 Cách sử dụng

### 1. Ch<PERSON>y tất cả test debug

```bash
# Chạy tất cả các test debug cho tenant system
npm run ts-node src/debug/run-tenant-debug.ts
```

### 2. Test riêng lẻ

```bash
# Test tenant context system
npm run ts-node src/debug/tenant-debug.ts

# Test API với tenant isolation
npm run ts-node src/debug/api-tenant-test.ts
```

### 3. Start server với debug logging

```bash
# Start server với tenant debug logging enabled
npm run ts-node src/debug/start-with-debug.ts
```

## 📋 Các script có sẵn

### `tenant-debug.ts`
- Kiểm tra tenant context system
- Test AsyncLocalStorage
- Verify tenantId injection
- Test với/không có tenantId
- Test disableTenantFilter

### `api-tenant-test.ts`
- Test API endpoints với tenant isolation
- Tạo JWT tokens cho các user khác nhau
- Verify tenant separation trong API responses
- Test CRUD operations với tenantId

### `run-tenant-debug.ts`
- Chạy tất cả test debug
- Tổng hợp kết quả
- Hiển thị summary và next steps

### `start-with-debug.ts`
- Start server với debug logging
- Enable tenant debug messages
- Highlight tenant-related logs

### `enable-debug-logging.ts`
- Enable/disable debug logging
- Highlight tenant logs với colors
- Override console methods

## 🔍 Cách đọc logs

### Log Levels
- `[TENANT-DEBUG]` (xanh lá): Debug information
- `[TENANT-WARNING]` (vàng): Warnings về tenant issues
- `[TENANT-ERROR]` (đỏ): Errors trong tenant system

### Các log quan trọng cần chú ý

#### TenantSecurityMiddleware
```
[TenantSecurityMiddleware] User 123 (type: EMPLOYEE) accessing GET /api/todos
[TenantSecurityMiddleware] User tenantId from JWT: 1
[TenantSecurityMiddleware] Set request.tenantId = 1 for user 123
```

#### TenantContextMiddleware
```
[TenantContextMiddleware] Processing request for user 123 (type: EMPLOYEE)
[TenantContextMiddleware] Request tenantId: 1, User tenantId: 1
[TenantContextMiddleware] Thiết lập tenantId 1 vào context cho user 123
```

#### TenantEntitySubscriber
```
[beforeQuery] Processing query for entity: Todo, alias: todo
[beforeQuery] Entity Todo has tenantId field. Current tenantId: 1
[beforeQuery] Added tenantId condition: todo.tenantId = 1 for entity Todo
```

## 🐛 Troubleshooting

### Vấn đề thường gặp

#### 1. Không có tenantId trong context
```
[TenantEntitySubscriber] No tenantId in context for entity Todo
```
**Nguyên nhân**: Middleware không được chạy hoặc user không có tenantId trong JWT
**Giải pháp**: Kiểm tra JWT token và middleware configuration

#### 2. User lấy được data của tenant khác
```
[beforeQuery] Entity Todo has tenantId field. Current tenantId: undefined
```
**Nguyên nhân**: TenantEntitySubscriber không inject tenantId vào query
**Giải pháp**: Kiểm tra AsyncLocalStorage và tenant context

#### 3. TenantId không được set khi tạo entity
```
[beforeInsert] No tenantId in context for entity Todo
```
**Nguyên nhân**: Không có tenantId trong context khi insert
**Giải pháp**: Đảm bảo middleware đã set tenantId vào context

### Debug steps

1. **Kiểm tra JWT token**
   - Verify tenantId có trong payload không
   - Check token expiration
   - Verify user type

2. **Kiểm tra middleware flow**
   - TenantSecurityMiddleware extract tenantId từ JWT
   - TenantContextMiddleware set vào AsyncLocalStorage
   - TenantEntitySubscriber inject vào queries

3. **Kiểm tra database queries**
   - Enable SQL logging
   - Verify WHERE clauses có tenantId
   - Check INSERT statements có tenantId

## 📊 Expected Results

### Khi hệ thống hoạt động đúng:

1. **User chỉ thấy data của tenant mình**
2. **Mọi query đều có điều kiện tenantId**
3. **Insert/Update tự động set tenantId**
4. **SYSTEM_ADMIN có thể access cross-tenant**

### Red flags:

1. **Query không có WHERE tenantId**
2. **User thấy data của tenant khác**
3. **Insert không có tenantId**
4. **Context không có tenantId**

## 🔧 Configuration

### Environment Variables
```bash
NODE_ENV=development
ENABLE_TENANT_DEBUG=true
LOG_LEVEL=debug
```

### Package.json Scripts (thêm vào)
```json
{
  "scripts": {
    "debug:tenant": "ts-node src/debug/run-tenant-debug.ts",
    "debug:tenant-context": "ts-node src/debug/tenant-debug.ts",
    "debug:tenant-api": "ts-node src/debug/api-tenant-test.ts",
    "start:debug-tenant": "ts-node src/debug/start-with-debug.ts"
  }
}
```
