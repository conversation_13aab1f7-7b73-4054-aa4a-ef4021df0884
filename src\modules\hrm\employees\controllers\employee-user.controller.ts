import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { EmployeeUserService } from '../services/employee-user.service';
import { CreateEmployeeWithUserDto } from '../dto/create-employee-with-user.dto';
import {
  EmployeeWithUserResponseDto,
  UserResponseDto,
} from '../dto/employee-with-user-response.dto';
import { CreateUserForEmployeeDto } from '../dto/create-user-for-employee.dto';
import { LinkEmployeeToUserDto } from '../dto/link-employee-to-user.dto';

/**
 * Controller cho việc quản lý nhân viên kèm tài khoản người dùng
 */
@ApiTags(SWAGGER_API_TAG.HRM)
@ApiExtraModels(ApiResponseDto, EmployeeWithUserResponseDto, UserResponseDto)
@Controller('api/hrm/employees/with-user')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeUserController {
  constructor(private readonly employeeUserService: EmployeeUserService) {}

  /**
   * Tạo nhân viên mới kèm tài khoản người dùng
   */
  @Post()
  @ApiOperation({ summary: 'Tạo nhân viên mới kèm tài khoản người dùng' })
  @ApiResponse({
    status: 201,
    description: 'Nhân viên và tài khoản đã được tạo thành công',
    schema: ApiResponseDto.getSchema(EmployeeWithUserResponseDto),
  })
  async create(
    @Body() createDto: CreateEmployeeWithUserDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<EmployeeWithUserResponseDto>> {
    const result = await this.employeeUserService.createEmployeeWithUser(
      createDto,
      user.id,
    );
    return ApiResponseDto.created(result);
  }

  /**
   * Tạo tài khoản người dùng cho nhân viên
   */
  @Post('create-user')
  @ApiOperation({ summary: 'Tạo tài khoản người dùng cho nhân viên' })
  @ApiResponse({
    status: 201,
    description: 'Tài khoản người dùng đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UserResponseDto),
  })
  async createUserForEmployee(
    @Body() createDto: CreateUserForEmployeeDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    const result = await this.employeeUserService.createUserForEmployee(
      createDto,
      user.id,
    );
    return ApiResponseDto.created(result);
  }

  /**
   * Gắn nhân viên với tài khoản người dùng
   */
  @Post('link-employee')
  @ApiOperation({ summary: 'Gắn nhân viên với tài khoản người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Nhân viên đã được gắn với tài khoản người dùng thành công',
    schema: ApiResponseDto.getSchema(EmployeeWithUserResponseDto),
  })
  async linkEmployeeToUser(
    @Body() linkDto: LinkEmployeeToUserDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<EmployeeWithUserResponseDto>> {
    const result = await this.employeeUserService.linkEmployeeToUser(
      linkDto,
      user.id,
    );
    return ApiResponseDto.success(result);
  }
}
