import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { OkrCycleService } from '../services/okr-cycle.service';
import {
  CreateOkrCycleDto,
  UpdateOkrCycleDto,
  OkrCycleQueryDto,
  OkrCycleResponseDto,
} from '../dto/okr-cycle';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { JwtPayload } from '@/modules/auth/guards';

/**
 * Controller cho chu kỳ OKR
 */
@ApiTags(SWAGGER_API_TAG.OKRS)
@Controller('api/okrs/cycles')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class OkrCycleController {
  constructor(private readonly okrCycleService: OkrCycleService) {}

  /**
   * Tạo mới chu kỳ OKR
   * @param user Người dùng hiện tại
   * @param dto DTO tạo chu kỳ OKR
   * @returns Phản hồi chu kỳ OKR đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới chu kỳ OKR' })
  @ApiResponse({
    status: 201,
    description: 'Chu kỳ OKR đã được tạo thành công.',
    type: ApiResponseDto,
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateOkrCycleDto,
  ): Promise<ApiResponseDto<OkrCycleResponseDto>> {
    const result = await this.okrCycleService.create(Number(user.tenantId), user.id, dto);
    return ApiResponseDto.created(result);
  }

  /**
   * Lấy tất cả chu kỳ OKR với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các phản hồi chu kỳ OKR
   */
  @Get()
  @ApiOperation({ summary: 'Lấy tất cả chu kỳ OKR' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách chu kỳ OKR.',
    type: ApiResponseDto,
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: OkrCycleQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<OkrCycleResponseDto>>> {
    const result = await this.okrCycleService.findAll(Number(user.tenantId), query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chu kỳ OKR đang hoạt động
   * @returns Phản hồi chu kỳ OKR đang hoạt động
   */
  @Get('active')
  @ApiOperation({ summary: 'Lấy chu kỳ OKR đang hoạt động' })
  @ApiResponse({
    status: 200,
    description: 'Chu kỳ OKR đang hoạt động.',
    type: ApiResponseDto,
  })
  async findActive(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<OkrCycleResponseDto>> {
    const result = await this.okrCycleService.findActive(Number(user.tenantId));
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy chu kỳ OKR theo ID
   * @param id ID chu kỳ OKR
   * @returns Phản hồi chu kỳ OKR
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chu kỳ OKR theo ID' })
  @ApiParam({ name: 'id', description: 'ID chu kỳ OKR' })
  @ApiResponse({
    status: 200,
    description: 'Chu kỳ OKR.',
    type: ApiResponseDto,
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<OkrCycleResponseDto>> {
    const result = await this.okrCycleService.findById(Number(user.tenantId), id);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật chu kỳ OKR
   * @param id ID chu kỳ OKR
   * @param dto DTO cập nhật chu kỳ OKR
   * @returns Phản hồi chu kỳ OKR đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật chu kỳ OKR' })
  @ApiParam({ name: 'id', description: 'ID chu kỳ OKR' })
  @ApiResponse({
    status: 200,
    description: 'Chu kỳ OKR đã được cập nhật thành công.',
    type: ApiResponseDto,
  })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateOkrCycleDto,
  ): Promise<ApiResponseDto<OkrCycleResponseDto>> {
    const result = await this.okrCycleService.update(Number(user.tenantId), id, dto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa chu kỳ OKR
   * @param id ID chu kỳ OKR
   * @returns Thông báo thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa chu kỳ OKR' })
  @ApiParam({ name: 'id', description: 'ID chu kỳ OKR' })
  @ApiResponse({
    status: 200,
    description: 'Chu kỳ OKR đã được xóa thành công.',
    type: ApiResponseDto,
  })
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.okrCycleService.delete(Number(user.tenantId), id);
    return ApiResponseDto.deleted(result);
  }
}
