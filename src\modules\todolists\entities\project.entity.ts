import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TenantEntity } from '@/common/subscribers/tenant-entity.subscriber';

/**
 * Entity representing projects
 */
@Entity('projects')
export class Project implements TenantEntity {
  /**
   * Unique identifier for the project
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Project title
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Detailed description of the project
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * ID of the user who manages the project
   */
  @Column({ name: 'owner_id', type: 'integer', nullable: false })
  ownerId: number;

  /**
   * ID of the user who created the project
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * Whether the project is active
   */
  @Column({ name: 'is_active', type: 'boolean', default: true, nullable: true })
  isActive: boolean | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number = 0;
}
