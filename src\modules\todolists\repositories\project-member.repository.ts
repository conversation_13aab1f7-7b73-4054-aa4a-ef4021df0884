import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectMember } from '../entities/project-members.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { QueryDto } from '@/common/dto/query.dto';
import { ProjectMemberRole } from '../enum/project-member-role.enum';

/**
 * Repository cho entity ProjectMember
 */
@Injectable()
export class ProjectMemberRepository {
  constructor(
    @InjectRepository(ProjectMember)
    private readonly repository: Repository<ProjectMember>,
  ) {}

  /**
   * Tạo thành viên dự án mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu thành viên dự án
   * @returns Thành viên dự án đã tạo
   */
  async create(tenantId: number, data: Partial<ProjectMember>): Promise<ProjectMember> {
    const projectMember = this.repository.create({ ...data, tenantId });
    return this.repository.save(projectMember);
  }

  /**
   * Tìm tất cả thành viên của dự án với phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param query Tham số truy vấn
   * @returns Danh sách thành viên dự án đã phân trang
   */
  async findAllByProjectId(
    tenantId: number,
    projectId: number,
    query: QueryDto,
  ): Promise<PaginatedResult<ProjectMember>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('projectMember');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.where('projectMember.projectId = :projectId AND projectMember.tenantId = :tenantId', {
      projectId,
      tenantId
    });

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`projectMember.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm thành viên dự án theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thành viên dự án
   * @returns Thành viên dự án hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<ProjectMember | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm thành viên dự án theo ID dự án và ID người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns Thành viên dự án hoặc null nếu không tìm thấy
   */
  async findByProjectIdAndUserId(
    tenantId: number,
    projectId: number,
    userId: number,
  ): Promise<ProjectMember | null> {
    return this.repository.findOne({
      where: {
        projectId,
        userId,
        tenantId,
      },
    });
  }

  /**
   * Kiểm tra người dùng có phải là thành viên của dự án không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns true nếu là thành viên, false nếu không phải
   */
  async isProjectMember(tenantId: number, projectId: number, userId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        projectId,
        userId,
        tenantId,
      },
    });
    return count > 0;
  }

  /**
   * Kiểm tra người dùng có phải là admin của dự án không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns true nếu là admin, false nếu không phải
   */
  async isProjectAdmin(tenantId: number, projectId: number, userId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        projectId,
        userId,
        role: ProjectMemberRole.ADMIN,
        tenantId,
      },
    });
    return count > 0;
  }

  /**
   * Cập nhật thành viên dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thành viên dự án
   * @param data Dữ liệu cập nhật
   * @returns Thành viên dự án đã cập nhật
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<ProjectMember>,
  ): Promise<ProjectMember | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Xóa thành viên dự án
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thành viên dự án
   * @returns Kết quả xóa
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa thành viên dự án theo ID dự án và ID người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  async deleteByProjectIdAndUserId(
    tenantId: number,
    projectId: number,
    userId: number,
  ): Promise<boolean> {
    const result = await this.repository.delete({
      projectId,
      userId,
      tenantId,
    });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
