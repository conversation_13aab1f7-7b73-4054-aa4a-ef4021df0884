import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ObjectiveService } from '../services/objective.service';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';

import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { JwtPayload, JwtUserGuard } from '@/modules/auth/guards';
import { CreateObjectiveDto } from '../dto/objective/create-objective.dto';
import { ObjectiveResponseDto } from '../dto/objective/objective-response.dto';
import { ObjectiveQueryDto } from '../dto/objective/objective-query.dto';
import { UpdateObjectiveDto } from '../dto/objective/update-objective.dto';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller cho mục tiêu
 */
@ApiTags(SWAGGER_API_TAG.OKRS)
@UseGuards(JwtUserGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
@Controller('api/okrs/objectives')
export class ObjectiveController {
  constructor(private readonly objectiveService: ObjectiveService) {}

  /**
   * Tạo mới mục tiêu
   * @param user Người dùng hiện tại
   * @param dto DTO tạo mục tiêu
   * @returns Phản hồi mục tiêu đã tạo
   */
  @Post()
  @RequirePermissionEnum(Permission.OKR_CREATE_OBJECTIVE)
  @ApiOperation({ summary: 'Tạo mới mục tiêu' })
  @ApiResponse({
    status: 201,
    description: 'Mục tiêu đã được tạo thành công.',
    type: ApiResponseDto,
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateObjectiveDto,
  ): Promise<ApiResponseDto<ObjectiveResponseDto>> {
    const result = await this.objectiveService.create(Number(user.tenantId), user.id, dto);
    return ApiResponseDto.created(result);
  }

  /**
   * Lấy tất cả mục tiêu với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các phản hồi mục tiêu
   */
  @Get()
  @RequirePermissionEnum(Permission.OKR_VIEW_LIST)
  @ApiOperation({ summary: 'Lấy tất cả mục tiêu' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách mục tiêu.',
    type: ApiResponseDto,
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: ObjectiveQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ObjectiveResponseDto>>> {
    const result = await this.objectiveService.findAll(Number(user.tenantId), query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy mục tiêu theo ID
   * @param id ID mục tiêu
   * @returns Phản hồi mục tiêu
   */
  @Get(':id')
  @RequirePermissionEnum(Permission.OKR_VIEW_DETAIL)
  @ApiOperation({ summary: 'Lấy mục tiêu theo ID' })
  @ApiParam({ name: 'id', description: 'ID mục tiêu' })
  @ApiResponse({
    status: 200,
    description: 'Mục tiêu.',
    type: ApiResponseDto,
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ObjectiveResponseDto>> {
    const result = await this.objectiveService.findById(Number(user.tenantId), id);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật mục tiêu
   * @param id ID mục tiêu
   * @param dto DTO cập nhật mục tiêu
   * @returns Phản hồi mục tiêu đã cập nhật
   */
  @Put(':id')
  @RequirePermissionEnum(Permission.OKR_UPDATE_OBJECTIVE)
  @ApiOperation({ summary: 'Cập nhật mục tiêu' })
  @ApiParam({ name: 'id', description: 'ID mục tiêu' })
  @ApiResponse({
    status: 200,
    description: 'Mục tiêu đã được cập nhật thành công.',
    type: ApiResponseDto,
  })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateObjectiveDto,
  ): Promise<ApiResponseDto<ObjectiveResponseDto>> {
    const result = await this.objectiveService.update(Number(user.tenantId), id, dto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa mục tiêu
   * @param id ID mục tiêu
   * @returns Thông báo thành công
   */
  @Delete(':id')
  @RequirePermissionEnum(Permission.OKR_DELETE_OBJECTIVE)
  @ApiOperation({ summary: 'Xóa mục tiêu' })
  @ApiParam({ name: 'id', description: 'ID mục tiêu' })
  @ApiResponse({
    status: 200,
    description: 'Mục tiêu đã được xóa thành công.',
    type: ApiResponseDto,
  })
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.objectiveService.delete(Number(user.tenantId), id);
    return ApiResponseDto.deleted(result);
  }

  /**
   * Cập nhật tiến độ mục tiêu
   * @param id ID mục tiêu
   * @param body Giá trị tiến độ
   * @returns Phản hồi mục tiêu đã cập nhật
   */
  @Put(':id/progress')
  @ApiOperation({ summary: 'Cập nhật tiến độ mục tiêu' })
  @ApiParam({ name: 'id', description: 'ID mục tiêu' })
  @ApiResponse({
    status: 200,
    description: 'Tiến độ mục tiêu đã được cập nhật thành công.',
    type: ApiResponseDto,
  })
  async updateProgress(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body('progress', ParseIntPipe) progress: number,
  ): Promise<ApiResponseDto<ObjectiveResponseDto>> {
    const result = await this.objectiveService.updateProgress(Number(user.tenantId), id, progress);
    return ApiResponseDto.success(result);
  }
}
