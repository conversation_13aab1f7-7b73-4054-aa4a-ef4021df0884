import { Logger } from '@nestjs/common';

/**
 * <PERSON><PERSON>t để enable debug logging cho tenant system
 * Thêm vào main.ts hoặc chạy trước khi test
 */
export function enableTenantDebugLogging() {
  const logger = new Logger('TenantDebugSetup');
  
  // Set log levels để hiển thị debug logs
  process.env.LOG_LEVEL = 'debug';
  
  // Override console để capture tất cả logs
  const originalLog = console.log;
  const originalDebug = console.debug;
  const originalWarn = console.warn;
  const originalError = console.error;
  
  console.log = (...args) => {
    if (args[0]?.includes?.('[TenantEntitySubscriber]') || 
        args[0]?.includes?.('[TenantSecurityMiddleware]') || 
        args[0]?.includes?.('[TenantContextMiddleware]')) {
      originalLog('\x1b[32m[TENANT-DEBUG]\x1b[0m', ...args);
    } else {
      originalLog(...args);
    }
  };
  
  console.debug = (...args) => {
    if (args[0]?.includes?.('[TenantEntitySubscriber]') || 
        args[0]?.includes?.('[TenantSecurityMiddleware]') || 
        args[0]?.includes?.('[TenantContextMiddleware]')) {
      originalDebug('\x1b[36m[TENANT-DEBUG]\x1b[0m', ...args);
    } else {
      originalDebug(...args);
    }
  };
  
  console.warn = (...args) => {
    if (args[0]?.includes?.('[TenantEntitySubscriber]') || 
        args[0]?.includes?.('[TenantSecurityMiddleware]') || 
        args[0]?.includes?.('[TenantContextMiddleware]')) {
      originalWarn('\x1b[33m[TENANT-WARNING]\x1b[0m', ...args);
    } else {
      originalWarn(...args);
    }
  };
  
  console.error = (...args) => {
    if (args[0]?.includes?.('[TenantEntitySubscriber]') || 
        args[0]?.includes?.('[TenantSecurityMiddleware]') || 
        args[0]?.includes?.('[TenantContextMiddleware]')) {
      originalError('\x1b[31m[TENANT-ERROR]\x1b[0m', ...args);
    } else {
      originalError(...args);
    }
  };
  
  logger.log('Tenant debug logging enabled');
  logger.log('Logs from TenantEntitySubscriber, TenantSecurityMiddleware, and TenantContextMiddleware will be highlighted');
}

/**
 * Disable debug logging
 */
export function disableTenantDebugLogging() {
  delete process.env.LOG_LEVEL;
  const logger = new Logger('TenantDebugSetup');
  logger.log('Tenant debug logging disabled');
}

// Auto-enable nếu được import
if (process.env.NODE_ENV === 'development') {
  enableTenantDebugLogging();
}
