# Product Requirements Document: Manual TenantId Implementation

## Project Overview
Implement manual tenantId filtering across the entire backend application to ensure proper tenant isolation. This involves systematically reviewing and updating all database queries, repositories, services, and controllers to include tenantId conditions.

## Background
The current automatic tenantId injection mechanisms (TenantEntitySubscriber, AsyncLocalStorage, helper functions) are unreliable and complex. We need to implement a simple, explicit approach where tenantId is manually added to all database operations.

## Objectives
1. **Complete Tenant Isolation**: Ensure all database queries include tenantId filtering
2. **Data Security**: Prevent cross-tenant data access
3. **Maintainability**: Use simple, explicit tenantId handling
4. **Performance**: Optimize queries with proper tenantId indexing
5. **Testing**: Verify tenant isolation works correctly

## Scope

### In Scope
- All repository classes and their query methods
- All service classes that interact with repositories
- All controller endpoints that handle tenant-specific data
- Database query builders and raw SQL queries
- Entity relationships and joins
- Pagination and filtering logic
- Create, Read, Update, Delete operations
- Search and reporting functionality

### Out of Scope
- System-level entities (users, companies, permissions)
- Public endpoints without tenant context
- Authentication and authorization logic
- Database schema changes

## Technical Requirements

### 1. Repository Layer Updates
- Add tenantId parameter to all repository methods
- Update all TypeORM find/findOne/createQueryBuilder calls
- Add tenantId to WHERE clauses in all queries
- Handle entity relationships with tenantId filtering
- Update pagination logic to include tenantId

### 2. Service Layer Updates
- Pass tenantId from controllers to repositories
- Update business logic to handle tenantId
- Ensure all repository calls include tenantId
- Update validation logic for tenant-specific data

### 3. Controller Layer Updates
- Use @CurrentUser() decorator to extract tenantId from JWT payload
- Pass Number(user.tenantId) as first parameter to service methods
- Update API documentation for tenant-aware endpoints
- Add tenantId validation where needed

### 4. Entity Relationships
- Update JOIN queries to include tenantId filtering
- Handle foreign key relationships across tenants
- Ensure cascade operations respect tenant boundaries

### 5. Testing Strategy
- Unit tests for repository methods with tenantId
- Integration tests for service layer
- End-to-end tests for API endpoints
- Security tests to verify tenant isolation

## Modules to Update

### ✅ COMPLETED MODULES
1. **Todolists Module** (100% COMPLETE)
   - ✅ ProjectController, ProjectService, ProjectRepository
   - ✅ TodoController, TodoService, TodoRepository
   - ✅ TodoTagService, TodoTagRepository
   - ✅ All related repositories (ProjectMember, TodoCollaborator, TodoAttachment, TaskKr)

### 🔄 IN PROGRESS MODULES
2. **HRM Module** (70% COMPLETE)
   - ✅ Employee Module (EmployeeController, EmployeeService, EmployeeRepository)
   - ✅ Org-Units Module (Department, DepartmentMembers, DepartmentTree)
   - ❌ Contracts Module (ContractController, ContractService, ContractRepository)
   - ❌ Attendance Management Module
   - ❌ Human Resources Module
   - ❌ Recruitment Module

### ❌ PENDING MODULES
3. **OKRs Module** (objectives, key-results, cycles)
4. **Calendar Module** (events, schedules)
5. **Chat Module** (messages, conversations)
6. **Notifications Module** (notifications, alerts)
7. **File Management Module** (uploads, documents)
8. **Reports Module** (analytics, exports)
9. **Email Module** (templates, logs)
10. **Common Module** (audit logs, system data)

## Implementation Strategy

### Phase 1: Repository Layer (Week 1)
- Update all repository classes to accept tenantId parameters
- Add tenantId to all WHERE clauses
- Update query builders and raw SQL queries
- Test repository methods in isolation

### Phase 2: Service Layer (Week 2)
- Update service methods to pass tenantId to repositories
- Ensure business logic respects tenant boundaries
- Update validation and error handling

### Phase 3: Controller Layer (Week 3)
- Update controllers to use @CurrentUser() decorator
- Pass Number(user.tenantId) to service methods
- Update API documentation and Swagger specs

### Phase 4: Testing & Validation (Week 4)
- Comprehensive testing of tenant isolation
- Performance testing with tenantId indexes
- Security testing to prevent cross-tenant access
- Documentation updates

## Success Criteria
1. All database queries include tenantId filtering
2. No cross-tenant data access possible
3. All tests pass with tenant isolation
4. Performance meets requirements
5. Code is maintainable and well-documented

## Risk Mitigation
- Systematic approach to avoid missing any queries
- Comprehensive testing at each layer
- Code review process for all changes
- Rollback plan if issues are discovered

## Deliverables
1. Updated repository classes with tenantId filtering
2. Updated service classes with tenantId handling
3. Updated controller classes with @CurrentTenant() usage
4. Comprehensive test suite
5. Updated documentation
6. Performance optimization recommendations

## Current Progress Status
- **Todolists Module**: 100% Complete ✅
- **HRM Employee Module**: 100% Complete ✅
- **HRM Org-Units Module**: 100% Complete ✅
- **HRM Contracts Module**: 0% Complete ❌
- **Other HRM Modules**: 0% Complete ❌
- **All Other Modules**: 0% Complete ❌

**Overall Progress: ~25% Complete**
