import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class SendEmailDto {
  @IsEmail()
  @IsNotEmpty()
  to: string;

  @IsString()
  @IsNotEmpty()
  subject: string;

  @IsString()
  @IsNotEmpty()
  body: string; // Nội dung HTML của email
}

/**
 * Interface đại diện cho dữ liệu từ EntityProvider.java.
 * Chứa các ID hoặc thông tin cần thiết để lấy dữ liệu cho email template.
 */
export interface EntityProviderData {
    userId?: number;
    orderId?: number;
    employeeId?: number;
    withDrawId?: number;
    twoFACode?: string;
    tokenVerifyEmail?: string;
    newPassword?: string;
    pointId?: string; // Có thể là string hoặc number tùy thuộc vào kiểu ID của Point
    vndCouponId?: number;
    rankId?: number;
    flashSaleId?: number;
    facebookPageId?: string;
    paymentGatewayId?: number;
    chatBotId?: number; // Giả sử chatBotId là number
    ticketId?: string;
    featureRequireId?: number;
} 