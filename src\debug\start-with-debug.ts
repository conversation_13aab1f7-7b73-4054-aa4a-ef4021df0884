import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe, VersioningType, Logger } from '@nestjs/common';
import { createSwaggerConfig, swaggerCustomOptions } from '../common/swagger';
import { ConfigService } from '@nestjs/config';
import { corsConfig } from '../common/filters/cors.config';
import { enableTenantDebugLogging } from './enable-debug-logging';

/**
 * Script để start server với debug logging enabled
 * Chạy: npm run start:debug-tenant
 */
async function startWithDebug() {
  const logger = new Logger('DebugServer');
  
  try {
    // Enable tenant debug logging
    enableTenantDebugLogging();
    
    // Set environment variables for debug
    process.env.NODE_ENV = 'development';
    process.env.ENABLE_TENANT_DEBUG = 'true';
    process.env.LOG_LEVEL = 'debug';
    
    logger.log('🚀 Starting server with tenant debug logging enabled...');
    
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'debug', 'error', 'verbose', 'warn']
    });

    // Thêm global prefix với version
    app.enableVersioning({
      type: VersioningType.URI,
      defaultVersion: '1',
    });

    // Cấu hình validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // Cấu hình CORS
    app.enableCors(corsConfig);

    // Cấu hình Swagger
    const configService = app.get(ConfigService);
    const swaggerConfig = createSwaggerConfig(configService);
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('api/docs', app, document, swaggerCustomOptions);

    // Khởi động server
    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    
    logger.log(`🌐 Application is running on: http://localhost:${port}`);
    logger.log(`📚 Swagger documentation: http://localhost:${port}/api/docs`);
    logger.log('🔍 Tenant debug logging is ENABLED');
    logger.log('📋 Watch for [TENANT-DEBUG], [TENANT-WARNING], [TENANT-ERROR] messages');
    logger.log('');
    logger.log('💡 Test tenant isolation with these endpoints:');
    logger.log('  - GET /api/v1/todos (with different user tokens)');
    logger.log('  - POST /api/v1/todos (create todos with different tenants)');
    logger.log('  - Check logs to verify tenantId injection');
    logger.log('');
    logger.log('🛑 Press Ctrl+C to stop the server');
    
  } catch (error) {
    logger.error('❌ Failed to start debug server:', error.message);
    logger.error(error.stack);
    process.exit(1);
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  startWithDebug().catch((error) => {
    console.error('Failed to start debug server:', error);
    process.exit(1);
  });
}

export { startWithDebug };
