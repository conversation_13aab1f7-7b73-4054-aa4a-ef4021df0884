import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@/common';
import { QueueService } from '@shared/queue/queue.service';
import { EmailJobData, TemplateEmailJobData } from '@shared/queue/queue.types';

/**
 * Service xử lý gửi email hệ thống
 * Sử dụng cấu hình email mặc định từ biến môi trường
 */
@Injectable()
export class SystemEmailService {
  private readonly logger = new Logger(SystemEmailService.name);

  constructor(
    private readonly queueService: QueueService,
    private readonly configService: ConfigService
  ) {}

  /**
   * Gửi email hệ thống
   * @param data Dữ liệu email cần gửi: { to, subject, content, cc?, bcc?, attachments? }
   * @returns Promise với job ID
   */
  async sendEmail(data: EmailJobData): Promise<string | undefined> {
    try {
      // Kiểm tra tham số bắt buộc
      if (!data.to) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu địa chỉ email người nhận'
        );
      }

      if (!data.subject) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu tiêu đề email'
        );
      }

      if (!data.content) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu nội dung email'
        );
      }

      // Thêm timestamp nếu chưa có
      if (!data.timestamp) {
        data.timestamp = Date.now();
      }

      // Thêm thông tin người gửi nếu chưa có
      if (!data.from) {
        data.from = this.configService.get<string>('EMAIL_FROM');
      }

      this.logger.log(`Chuẩn bị gửi email hệ thống đến: ${data.to}`);

      // Thêm job vào queue để xử lý bất đồng bộ
      const jobId = await this.queueService.addSystemEmailJob(data);

      this.logger.log(`Đã thêm email hệ thống vào queue với jobId: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email hệ thống: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email hệ thống');
    }
  }

  /**
   * Gửi email hệ thống theo mẫu
   * @param data Dữ liệu template email cần gửi: { to, templateId, data, cc?, bcc? }
   * @returns Promise với job ID
   */
  async sendTemplateEmail(data: TemplateEmailJobData): Promise<string | undefined> {
    try {
      // Kiểm tra tham số bắt buộc
      if (!data.to) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu địa chỉ email người nhận'
        );
      }

      if (!data.templateId) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu ID mẫu email'
        );
      }

      if (!data.data || Object.keys(data.data).length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thiếu dữ liệu cho mẫu email'
        );
      }

      // Thêm timestamp nếu chưa có
      if (!data.timestamp) {
        data.timestamp = Date.now();
      }

      this.logger.log(`Chuẩn bị gửi email mẫu hệ thống ${data.templateId} đến: ${data.to}`);

      // Thêm job vào queue để xử lý bất đồng bộ với độ ưu tiên cao
      const jobId = await this.queueService.addSystemTemplateEmailJob(data);

      this.logger.log(`Đã thêm email mẫu hệ thống vào queue với jobId: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi email mẫu hệ thống: ${error.message}`, error.stack);
      throw error instanceof AppException
        ? error
        : new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email mẫu hệ thống');
    }
  }
} 