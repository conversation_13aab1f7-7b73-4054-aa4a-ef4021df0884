import { Client } from 'pg';
import { config } from 'dotenv';

// Đ<PERSON><PERSON> biến môi trường từ file .env
config();

/**
 * Script đơn giản để kiểm tra tenantId trong bảng todos
 */
async function testTenantIdInTodos() {
  console.log('Starting simple tenant ID test...');

  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'postgres',
    ssl:
      process.env.DB_SSL === 'true'
        ? {
            rejectUnauthorized: false,
          }
        : false,
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully!');

    // <PERSON><PERSON><PERSON> tra cấu trúc bảng todos
    console.log('Checking todos table structure...');
    const tableInfo = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'todos'
      ORDER BY ordinal_position
    `);

    console.log('Todos table structure:');
    console.table(tableInfo.rows);

    // Kiểm tra ràng buộc khóa ngoại
    console.log('Checking foreign key constraints...');
    const fkConstraints = await client.query(`
      SELECT
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'todos'
    `);

    console.log('Foreign key constraints:');
    console.table(fkConstraints.rows);

    // Kiểm tra dữ liệu hiện có
    console.log('Checking existing todos data...');
    const existingTodos = await client.query(`
      SELECT id, title, tenant_id
      FROM todos
      LIMIT 10
    `);

    console.log(`Found ${existingTodos.rows.length} todos`);
    if (existingTodos.rows.length > 0) {
      console.log('Sample todos:');
      console.table(existingTodos.rows);
    }

    // Kiểm tra companies table
    console.log('Checking companies table...');
    try {
      const companies = await client.query(`
        SELECT id, name
        FROM companies
        LIMIT 5
      `);

      console.log(`Found ${companies.rows.length} companies`);
      if (companies.rows.length > 0) {
        console.log('Sample companies:');
        console.table(companies.rows);
      }
    } catch (error) {
      console.error('Error querying companies table:', error.message);
    }

    // Thử tạo một todo với tenantId
    console.log('Trying to create a todo with tenantId...');
    try {
      // Lấy một tenantId hợp lệ từ bảng companies
      const companies = await client.query(`
        SELECT id FROM companies LIMIT 1
      `);

      let validTenantId = 999; // Giá trị mặc định
      if (companies.rows.length > 0) {
        validTenantId = companies.rows[0].id;
        console.log(`Using valid tenantId: ${validTenantId}`);
      } else {
        console.warn('No companies found, using test tenantId');
      }

      // Tạo một todo mới với tenantId
      const result = await client.query(
        `
        INSERT INTO todos (title, description, assignee_id, priority, expected_stars, tenant_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, title, tenant_id
      `,
        ['Test Todo', 'This is a test todo', 1, 'medium', 3, validTenantId],
      );

      console.log('Created todo:');
      console.table(result.rows);

      // Xóa todo test
      await client.query(
        `
        DELETE FROM todos WHERE id = $1
      `,
        [result.rows[0].id],
      );

      console.log(`Deleted test todo with ID: ${result.rows[0].id}`);
    } catch (error) {
      console.error('Error creating todo:', error.message);
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Đóng kết nối
    await client.end();
    console.log('Connection closed');
  }
}

// Chạy test
testTenantIdInTodos().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
