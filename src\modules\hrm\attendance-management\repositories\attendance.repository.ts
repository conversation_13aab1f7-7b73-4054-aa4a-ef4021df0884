import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Attendance } from '../entities/attendance.entity';
import { AttendanceQueryDto } from '../dto/attendance-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AttendanceStatus } from '../enum/attendance-status.enum';

/**
 * Repository for attendance management with tenant isolation
 */
@Injectable()
export class AttendanceRepository {
  private readonly logger = new Logger(AttendanceRepository.name);

  constructor(
    @InjectRepository(Attendance)
    private readonly repository: Repository<Attendance>,
  ) {}

  /**
   * Find all attendance records with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of attendance records
   */
  async findAll(tenantId: number, query: AttendanceQueryDto): Promise<PaginatedResult<Attendance>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'workDate',
      sortDirection = 'DESC',
      employeeId,
      status,
      workDateFrom,
      workDateTo,
      minWorkHours,
      maxWorkHours,
      isManual,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('attendance');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('attendance.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (employeeId) {
      queryBuilder.andWhere('attendance.employeeId = :employeeId', { employeeId });
    }

    if (status) {
      queryBuilder.andWhere('attendance.status = :status', { status });
    }

    if (workDateFrom) {
      queryBuilder.andWhere('attendance.workDate >= :workDateFrom', { workDateFrom });
    }

    if (workDateTo) {
      queryBuilder.andWhere('attendance.workDate <= :workDateTo', { workDateTo });
    }

    if (minWorkHours !== undefined) {
      queryBuilder.andWhere('attendance.workHours >= :minWorkHours', { minWorkHours });
    }

    if (maxWorkHours !== undefined) {
      queryBuilder.andWhere('attendance.workHours <= :maxWorkHours', { maxWorkHours });
    }

    if (isManual !== undefined) {
      queryBuilder.andWhere('attendance.isManual = :isManual', { isManual });
    }

    // Apply search if provided
    if (search) {
      queryBuilder.andWhere(
        '(attendance.notes ILIKE :search OR CAST(attendance.employeeId AS TEXT) ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`attendance.${sortBy}`, sortDirection);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find attendance record by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Attendance ID
   * @returns Attendance record or null if not found
   */
  async findById(tenantId: number, id: number): Promise<Attendance | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find attendance records by employee ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @returns List of attendance records
   */
  async findByEmployeeId(tenantId: number, employeeId: number): Promise<Attendance[]> {
    return this.repository.find({
      where: { employeeId, tenantId },
      order: { workDate: 'DESC' },
    });
  }

  /**
   * Find attendance record by employee ID and work date
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param workDate Work date
   * @returns Attendance record or null if not found
   */
  async findByEmployeeIdAndDate(tenantId: number, employeeId: number, workDate: Date): Promise<Attendance | null> {
    return this.repository.findOne({
      where: { employeeId, workDate, tenantId },
    });
  }

  /**
   * Find attendance records within date range
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID (optional)
   * @param startDate Start date
   * @param endDate End date
   * @returns List of attendance records
   */
  async findByDateRange(
    tenantId: number,
    startDate: Date,
    endDate: Date,
    employeeId?: number
  ): Promise<Attendance[]> {
    const whereCondition: any = {
      tenantId,
      workDate: Between(startDate, endDate),
    };

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    return this.repository.find({
      where: whereCondition,
      order: { workDate: 'ASC', employeeId: 'ASC' },
    });
  }

  /**
   * Create a new attendance record
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Attendance data
   * @returns Created attendance record
   */
  async create(tenantId: number, data: Partial<Attendance>): Promise<Attendance> {
    const attendance = this.repository.create({ ...data, tenantId });
    return this.repository.save(attendance);
  }

  /**
   * Update attendance record
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Attendance ID
   * @param data Updated attendance data
   * @returns Updated attendance record or null if not found
   */
  async update(tenantId: number, id: number, data: Partial<Attendance>): Promise<Attendance | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete attendance record
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Attendance ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Check if employee has attendance record for specific date
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param workDate Work date
   * @returns True if exists, false otherwise
   */
  async existsByEmployeeIdAndDate(tenantId: number, employeeId: number, workDate: Date): Promise<boolean> {
    const count = await this.repository.count({
      where: { employeeId, workDate, tenantId },
    });
    return count > 0;
  }

  /**
   * Get attendance statistics for employee within date range
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param startDate Start date
   * @param endDate End date
   * @returns Attendance statistics
   */
  async getAttendanceStats(
    tenantId: number,
    employeeId: number,
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    totalWorkHours: number;
    totalOvertimeHours: number;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('attendance');

    queryBuilder
      .select([
        'COUNT(*) as totalDays',
        'SUM(CASE WHEN attendance.status = :presentStatus THEN 1 ELSE 0 END) as presentDays',
        'SUM(CASE WHEN attendance.status = :absentStatus THEN 1 ELSE 0 END) as absentDays',
        'SUM(CASE WHEN attendance.status = :lateStatus THEN 1 ELSE 0 END) as lateDays',
        'SUM(COALESCE(attendance.workHours, 0)) as totalWorkHours',
        'SUM(COALESCE(attendance.overtimeHours, 0)) as totalOvertimeHours',
      ])
      .where('attendance.tenantId = :tenantId', { tenantId })
      .andWhere('attendance.employeeId = :employeeId', { employeeId })
      .andWhere('attendance.workDate BETWEEN :startDate AND :endDate', { startDate, endDate })
      .setParameters({
        presentStatus: AttendanceStatus.PRESENT,
        absentStatus: AttendanceStatus.ABSENT,
        lateStatus: AttendanceStatus.LATE,
      });

    const result = await queryBuilder.getRawOne();

    return {
      totalDays: parseInt(result.totalDays) || 0,
      presentDays: parseInt(result.presentDays) || 0,
      absentDays: parseInt(result.absentDays) || 0,
      lateDays: parseInt(result.lateDays) || 0,
      totalWorkHours: parseInt(result.totalWorkHours) || 0,
      totalOvertimeHours: parseInt(result.totalOvertimeHours) || 0,
    };
  }
}
