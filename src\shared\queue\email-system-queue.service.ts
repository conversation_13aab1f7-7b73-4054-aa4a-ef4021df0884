import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AppException, ErrorCode } from '@/common';
import { QueueName, EmailSystemJobName, DEFAULT_JOB_OPTIONS, HIGH_PRIORITY_JOB_OPTIONS } from './queue.constants';
import { JobOptions } from './queue.types';

/**
 * DTO cho dữ liệu job gửi email
 */
export interface EmailSystemJobDto {
  /**
   * Danh mục email (category trong admin_template_email)
   */
  category: string;

  /**
   * Dữ liệu để thay thế các placeholder trong template
   * Dạng {key: string, key: string...}
   */
  data: Record<string, any>;

  /**
   * Địa chỉ email người nhận
   */
  to: string | string[];
}

/**
 * Service quản lý việc thêm job vào queue EMAIL_SYSTEM
 */
@Injectable()
export class EmailSystemQueueService {
  private readonly logger = new Logger(EmailSystemQueueService.name);

  constructor(
    @InjectQueue(QueueName.EMAIL_SYSTEM) private readonly emailSystemQueue: Queue,
  ) {}

  /**
   * Thêm job gửi email hệ thống theo template vào queue
   * @param data Dữ liệu email system cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addEmailSystemJob(data: EmailSystemJobDto, opts?: JobOptions): Promise<string | undefined> {
    try {
      // Validate dữ liệu đầu vào
      this.validateEmailSystemJobData(data);

      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.emailSystemQueue.add(
        EmailSystemJobName.SEND_TEMPLATE_EMAIL,
        data,
        options
      );

      this.logger.log(`Đã thêm job email system vào queue: ${job.id} - Category: ${data.category} - To: ${data.to}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email system vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job email system vào queue');
    }
  }

  /**
   * Thêm job gửi email hệ thống với độ ưu tiên cao
   * @param data Dữ liệu email system cần gửi
   * @returns Promise với ID của job đã tạo
   */
  async addHighPriorityEmailSystemJob(data: EmailSystemJobDto): Promise<string | undefined> {
    const highPriorityOptions: JobOptions = {
      ...HIGH_PRIORITY_JOB_OPTIONS,
      priority: 1, // Ưu tiên cao nhất
      attempts: 5, // Nhiều lần thử lại
      backoff: {
        type: 'exponential' as const,
        delay: 2000,
      },
    };

    return this.addEmailSystemJob(data, highPriorityOptions);
  }

  /**
   * Thêm job gửi email hệ thống với delay
   * @param data Dữ liệu email system cần gửi
   * @param delayMs Thời gian delay tính bằng milliseconds
   * @returns Promise với ID của job đã tạo
   */
  async addDelayedEmailSystemJob(data: EmailSystemJobDto, delayMs: number): Promise<string | undefined> {
    const delayedOptions: JobOptions = {
      ...DEFAULT_JOB_OPTIONS,
      delay: delayMs,
      backoff: {
        type: 'exponential' as const,
        delay: 1000,
      },
    };

    return this.addEmailSystemJob(data, delayedOptions);
  }

  /**
   * Kiểm tra trạng thái của job email system
   * @param jobId ID của job
   * @returns Thông tin về job
   */
  async getEmailSystemJobStatus(jobId: string | undefined): Promise<any> {
    try {
      if (!jobId) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'ID job không hợp lệ');
      }

      const job = await this.emailSystemQueue.getJob(jobId);

      if (!job) {
        throw new AppException(ErrorCode.NOT_FOUND, `Job không tồn tại: ${jobId}`);
      }

      return {
        id: job.id,
        name: job.name,
        data: job.data,
        opts: job.opts,
        progress: job.progress(),
        timestamp: job.timestamp,
        attemptsMade: job.attemptsMade,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        returnvalue: job.returnvalue,
        finishedOn: job.finishedOn,
        processedOn: job.processedOn,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy trạng thái job: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lấy trạng thái job');
    }
  }

  /**
   * Hủy job email system
   * @param jobId ID của job cần hủy
   * @returns Promise<void>
   */
  async cancelEmailSystemJob(jobId: string | undefined): Promise<void> {
    try {
      if (!jobId) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'ID job không hợp lệ');
      }

      const job = await this.emailSystemQueue.getJob(jobId);

      if (!job) {
        throw new AppException(ErrorCode.NOT_FOUND, `Job không tồn tại: ${jobId}`);
      }

      await job.remove();
      this.logger.log(`Đã hủy job email system: ${jobId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi hủy job email system: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể hủy job email system');
    }
  }

  /**
   * Validate dữ liệu EmailSystemJobDto
   * @param data Dữ liệu cần validate
   */
  private validateEmailSystemJobData(data: EmailSystemJobDto): void {
    if (!data.category || typeof data.category !== 'string') {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Category là bắt buộc và phải là string');
    }

    if (!data.to || (typeof data.to !== 'string' && !Array.isArray(data.to))) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'To là bắt buộc và phải là string hoặc array');
    }

    if (Array.isArray(data.to) && data.to.length === 0) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'To array không được rỗng');
    }

    if (!data.data || typeof data.data !== 'object') {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Data là bắt buộc và phải là object');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const emails = Array.isArray(data.to) ? data.to : [data.to];

    for (const email of emails) {
      if (!emailRegex.test(email)) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, `Email không hợp lệ: ${email}`);
      }
    }
  }

  /**
   * Lấy thống kê queue email system
   * @returns Thống kê queue
   */
  async getEmailSystemQueueStats(): Promise<any> {
    try {
      const waiting = await this.emailSystemQueue.getWaiting();
      const active = await this.emailSystemQueue.getActive();
      const completed = await this.emailSystemQueue.getCompleted();
      const failed = await this.emailSystemQueue.getFailed();
      const delayed = await this.emailSystemQueue.getDelayed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lấy thống kê queue');
    }
  }
}
