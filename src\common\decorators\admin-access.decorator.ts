import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { tenantContext } from '../subscribers/tenant-entity.subscriber';
import { AppException } from '@/common';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Decorator để tắt tenant filtering cho admin API
 * Sử dụng decorator này khi cần truy cập dữ liệu của tất cả các tenant
 * Chỉ SYSTEM_ADMIN mới có thể sử dụng decorator này
 *
 * @example
 * @Get()
 * findAll(@AdminAccess() admin: any) {
 *   // Tenant filtering đã bị tắt, có thể truy cập dữ liệu của tất cả các tenant
 *   return this.adminService.findAllAcrossTenants();
 * }
 */
export const AdminAccess = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    // Kiểm tra xem người dùng có phải là SYSTEM_ADMIN không
    if (user?.type !== 'SYSTEM_ADMIN') {
      throw new AppException(
        ErrorCode.FORBIDDEN,
        'Chỉ SYSTEM_ADMIN mới có thể truy cập API này',
      );
    }

    // Tạm thời tắt tenant filtering
    const currentStore = tenantContext.getStore();
    const tenantId = currentStore?.tenantId ?? -1; // Default to -1 if undefined

    tenantContext.enterWith({ tenantId, disableTenantFilter: true });

    return user;
  },
);
