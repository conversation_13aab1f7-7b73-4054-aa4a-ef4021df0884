import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsBoolean, IsEnum } from 'class-validator';
import { CreateAttendanceDto } from './create-attendance.dto';
import { AttendanceStatus } from '../enum/attendance-status.enum';

/**
 * DTO for updating attendance record
 */
export class UpdateAttendanceDto extends PartialType(CreateAttendanceDto) {
  /**
   * Check-in time (timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Check-in time as timestamp in milliseconds',
    example: 1701417600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  checkInTime?: number;

  /**
   * Check-out time (timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Check-out time as timestamp in milliseconds',
    example: 1701446400000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  checkOutTime?: number;

  /**
   * Total work hours for the day (in minutes)
   */
  @ApiProperty({
    description: 'Total work hours in minutes',
    example: 480,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  workHours?: number;

  /**
   * Break time duration (in minutes)
   */
  @ApiProperty({
    description: 'Break time duration in minutes',
    example: 60,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  breakTime?: number;

  /**
   * Overtime hours (in minutes)
   */
  @ApiProperty({
    description: 'Overtime hours in minutes',
    example: 120,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  overtimeHours?: number;

  /**
   * Attendance status
   */
  @ApiProperty({
    description: 'Attendance status',
    enum: AttendanceStatus,
    example: AttendanceStatus.PRESENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(AttendanceStatus)
  status?: AttendanceStatus;

  /**
   * Notes or comments about the attendance
   */
  @ApiProperty({
    description: 'Notes or comments about the attendance',
    example: 'Updated due to system error',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  /**
   * Whether the attendance was manually adjusted
   */
  @ApiProperty({
    description: 'Whether the attendance was manually adjusted by admin',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isManual?: boolean;
}
