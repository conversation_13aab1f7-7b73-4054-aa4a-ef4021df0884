import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TenantEntity } from '@/shared/database/subscribers/tenant-subscriber';

/**
 * Entity representing employee benefit enrollments
 */
@Entity('employee_benefits')
export class EmployeeBenefit implements TenantEntity {
  /**
   * Unique identifier for the employee benefit enrollment
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the employee
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: false })
  employeeId: number;

  /**
   * ID of the benefit
   */
  @Column({ name: 'benefit_id', type: 'integer', nullable: false })
  benefitId: number;

  /**
   * Date when employee enrolled in benefit
   */
  @Column({ name: 'enrollment_date', type: 'date', nullable: false })
  enrollmentDate: Date;

  /**
   * Date when benefit coverage starts
   */
  @Column({ name: 'coverage_start_date', type: 'date', nullable: false })
  coverageStartDate: Date;

  /**
   * Date when benefit coverage ends
   */
  @Column({ name: 'coverage_end_date', type: 'date', nullable: true })
  coverageEndDate: Date | null;

  /**
   * Enrollment status (enrolled, pending, approved, rejected, cancelled)
   */
  @Column({ type: 'varchar', length: 50, nullable: false, default: 'enrolled' })
  status: string;

  /**
   * Employee's monthly contribution amount
   */
  @Column({ name: 'employee_contribution', type: 'decimal', precision: 10, scale: 2, nullable: true })
  employeeContribution: number | null;

  /**
   * Employer's monthly contribution amount
   */
  @Column({ name: 'employer_contribution', type: 'decimal', precision: 10, scale: 2, nullable: true })
  employerContribution: number | null;

  /**
   * Currency for contributions
   */
  @Column({ type: 'varchar', length: 10, default: 'VND' })
  currency: string;

  /**
   * Coverage level selected (individual, family, etc.)
   */
  @Column({ name: 'coverage_level', type: 'varchar', length: 100, nullable: true })
  coverageLevel: string | null;

  /**
   * Deductible amount for this enrollment
   */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  deductible: number | null;

  /**
   * Whether employee opted out of this benefit
   */
  @Column({ name: 'opted_out', type: 'boolean', default: false })
  optedOut: boolean;

  /**
   * Reason for opting out
   */
  @Column({ name: 'opt_out_reason', type: 'text', nullable: true })
  optOutReason: string | null;

  /**
   * Date when employee opted out
   */
  @Column({ name: 'opt_out_date', type: 'date', nullable: true })
  optOutDate: Date | null;

  /**
   * Beneficiary information (for life insurance, etc.)
   */
  @Column({ name: 'beneficiary_info', type: 'text', nullable: true })
  beneficiaryInfo: string | null;

  /**
   * Emergency contact information
   */
  @Column({ name: 'emergency_contact', type: 'text', nullable: true })
  emergencyContact: string | null;

  /**
   * Dependent information covered under this benefit
   */
  @Column({ name: 'dependent_info', type: 'text', nullable: true })
  dependentInfo: string | null;

  /**
   * Number of dependents covered
   */
  @Column({ name: 'dependent_count', type: 'integer', default: 0 })
  dependentCount: number;

  /**
   * Pre-existing conditions declared
   */
  @Column({ name: 'preexisting_conditions', type: 'text', nullable: true })
  preexistingConditions: string | null;

  /**
   * Medical questionnaire responses
   */
  @Column({ name: 'medical_questionnaire', type: 'text', nullable: true })
  medicalQuestionnaire: string | null;

  /**
   * ID of the HR person who processed the enrollment
   */
  @Column({ name: 'processed_by', type: 'integer', nullable: true })
  processedBy: number | null;

  /**
   * Date when enrollment was processed
   */
  @Column({ name: 'processed_at', type: 'bigint', nullable: true })
  processedAt: number | null;

  /**
   * ID of the manager who approved the enrollment
   */
  @Column({ name: 'approved_by', type: 'integer', nullable: true })
  approvedBy: number | null;

  /**
   * Date when enrollment was approved
   */
  @Column({ name: 'approved_at', type: 'bigint', nullable: true })
  approvedAt: number | null;

  /**
   * Enrollment confirmation number
   */
  @Column({ name: 'confirmation_number', type: 'varchar', length: 100, nullable: true })
  confirmationNumber: string | null;

  /**
   * Policy number assigned by provider
   */
  @Column({ name: 'policy_number', type: 'varchar', length: 100, nullable: true })
  policyNumber: string | null;

  /**
   * Additional enrollment notes
   */
  @Column({ type: 'text', nullable: true })
  notes: string | null;

  /**
   * Documents submitted with enrollment
   */
  @Column({ type: 'text', nullable: true })
  documents: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: false })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   * REQUIRED for tenant isolation
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
