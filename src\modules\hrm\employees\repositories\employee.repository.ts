import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Employee } from '../entities/employee.entity';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { EmployeeStatus } from '../enum/employee-status.enum';

/**
 * Repository for employee entity
 */
@Injectable()
export class EmployeeRepository {
  private readonly logger = new Logger(EmployeeRepository.name);

  constructor(
    @InjectRepository(Employee)
    private readonly repository: Repository<Employee>,
  ) {}

  /**
   * Find all employees with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of employees
   */
  async findAll(tenantId: number, query: EmployeeQueryDto): Promise<PaginatedResult<Employee>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'employeeCode',
      sortDirection = 'ASC',
      departmentId,
      managerId,
      status,
      employmentType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('employee');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('employee.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', {
        departmentId,
      });
    }

    if (managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
    }

    if (status) {
      queryBuilder.andWhere('employee.status = :status', { status });
    }

    if (employmentType) {
      queryBuilder.andWhere('employee.employmentType = :employmentType', {
        employmentType,
      });
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('employee.employeeCode ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`employee.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find employee by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @returns Employee or null if not found
   */
  async findById(tenantId: number, id: number): Promise<Employee | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find employee by user ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId User ID
   * @returns Employee or null if not found
   */
  async findByUserId(tenantId: number, userId: number): Promise<Employee | null> {
    return this.repository.findOne({
      where: { userId, tenantId },
    });
  }

  /**
   * Find employee by employee code
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeCode Employee code
   * @returns Employee or null if not found
   */
  async findByEmployeeCode(tenantId: number, employeeCode: string): Promise<Employee | null> {
    return this.repository.findOne({
      where: { employeeCode, tenantId },
    });
  }

  /**
   * Find employees by department ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param departmentId Department ID
   * @returns List of employees
   */
  async findByDepartmentId(tenantId: number, departmentId: number): Promise<Employee[]> {
    return this.repository.find({
      where: { departmentId, tenantId },
      order: { employeeCode: 'ASC' },
    });
  }

  /**
   * Find employees by manager ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param managerId Manager ID
   * @returns List of employees
   */
  async findByManagerId(tenantId: number, managerId: number): Promise<Employee[]> {
    return this.repository.find({
      where: { managerId, tenantId },
      order: { employeeCode: 'ASC' },
    });
  }

  /**
   * Create a new employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Employee data
   * @returns Created employee
   */
  async create(tenantId: number, data: Partial<Employee>): Promise<Employee> {
    const employee = this.repository.create({ ...data, tenantId });
    return this.repository.save(employee);
  }

  /**
   * Update employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param data Updated employee data
   * @returns Updated employee or null if not found
   */
  async update(tenantId: number, id: number, data: Partial<Employee>): Promise<Employee | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Update employee status
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param status New status
   * @returns Updated employee or null if not found
   */
  async updateStatus(
    tenantId: number,
    id: number,
    status: EmployeeStatus,
  ): Promise<Employee | null> {
    await this.repository.update({ id, tenantId }, { status });
    return this.findById(tenantId, id);
  }

  /**
   * Count employees by department
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns List of department IDs with employee counts
   */
  async countByDepartment(tenantId: number): Promise<
    { departmentId: number; count: number }[]
  > {
    const result = await this.repository
      .createQueryBuilder('employee')
      .select('employee.departmentId', 'departmentId')
      .addSelect('COUNT(employee.id)', 'count')
      .where('employee.departmentId IS NOT NULL AND employee.tenantId = :tenantId', { tenantId })
      .groupBy('employee.departmentId')
      .getRawMany();

    return result.map((item) => ({
      departmentId: parseInt(item.departmentId),
      count: parseInt(item.count),
    }));
  }
}
