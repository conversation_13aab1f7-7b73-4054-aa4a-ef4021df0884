import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, Repository } from 'typeorm';
import { Calendar } from '../entities/calendar.entity';
import { ReferenceType } from '../enum/reference-type.enum';
import { CalendarQueryDto } from '../dto/calendar-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho Calendar
 */
@Injectable()
export class CalendarRepository {
  private readonly logger = new Logger(CalendarRepository.name);

  constructor(
    @InjectRepository(Calendar)
    private readonly repository: Repository<Calendar>,
  ) {}

  /**
   * Tìm tất cả sự kiện lịch trong khoảng thời gian
   * @param tenantId ID tenant (required for tenant isolation)
   * @param startDate Thời gian bắt đầu
   * @param endDate Thời gian kết thúc
   * @param types <PERSON><PERSON><PERSON> loại sự kiện cần lấy
   * @returns Danh sách sự kiện lịch
   */
  async findByDateRange(
    tenantId: number,
    startDate: number,
    endDate: number,
    types?: ReferenceType[],
  ): Promise<Calendar[]> {
    try {
      const queryBuilder = this.repository.createQueryBuilder('calendar');

      // Add tenantId filtering - REQUIRED for tenant isolation
      queryBuilder.where('calendar.tenantId = :tenantId', { tenantId });

      // Điều kiện thời gian: sự kiện bắt đầu hoặc kết thúc trong khoảng thời gian
      // hoặc sự kiện bao trùm khoảng thời gian (bắt đầu trước startDate và kết thúc sau endDate)
      queryBuilder.andWhere(
        '(calendar.start_time BETWEEN :startDate AND :endDate) OR ' +
          '(calendar.end_time BETWEEN :startDate AND :endDate) OR ' +
          '(calendar.start_time <= :startDate AND calendar.end_time >= :endDate)',
        { startDate, endDate },
      );

      // Lọc theo loại sự kiện nếu có
      if (types && types.length > 0) {
        queryBuilder.andWhere('calendar.reference_type IN (:...types)', {
          types,
        });
      }

      return await queryBuilder.getMany();
    } catch (error) {
      this.logger.error(
        `Error finding calendar events by date range: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Tìm tất cả sự kiện lịch với phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Kết quả phân trang
   */
  async findAll(tenantId: number, query: CalendarQueryDto): Promise<PaginatedResult<Calendar>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'startTime',
        sortDirection = 'ASC',
        startDate,
        endDate,
        types,
      } = query;

      const queryBuilder = this.repository.createQueryBuilder('calendar');

      // Add tenantId filtering - REQUIRED for tenant isolation
      queryBuilder.where('calendar.tenantId = :tenantId', { tenantId });

      // Điều kiện thời gian
      queryBuilder.andWhere(
        '(calendar.start_time BETWEEN :startDate AND :endDate) OR ' +
          '(calendar.end_time BETWEEN :startDate AND :endDate) OR ' +
          '(calendar.start_time <= :startDate AND calendar.end_time >= :endDate)',
        { startDate, endDate },
      );

      // Lọc theo loại sự kiện nếu có
      if (types && types.length > 0) {
        queryBuilder.andWhere('calendar.reference_type IN (:...types)', {
          types,
        });
      }

      // Tìm kiếm theo mô tả hoặc thông tin trong JSON
      if (search) {
        queryBuilder.andWhere('calendar.description ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Sắp xếp
      queryBuilder.orderBy(`calendar.${sortBy}`, sortDirection);

      // Phân trang
      const [items, totalItems] = await queryBuilder
        .skip((page - 1) * limit)
        .take(limit)
        .getManyAndCount();

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error finding all calendar events: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm sự kiện lịch theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của sự kiện lịch
   * @returns Sự kiện lịch hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<Calendar | null> {
    return this.repository.findOne({ where: { id, tenantId } });
  }

  /**
   * Tìm sự kiện lịch theo tham chiếu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param referenceType Loại tham chiếu
   * @param referenceId ID tham chiếu
   * @returns Sự kiện lịch hoặc null nếu không tìm thấy
   */
  async findByReference(
    tenantId: number,
    referenceType: ReferenceType,
    referenceId: number,
  ): Promise<Calendar | null> {
    return this.repository.findOne({
      where: {
        referenceType,
        referenceId,
        tenantId,
      },
    });
  }

  /**
   * Tạo sự kiện lịch mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu sự kiện lịch
   * @returns Sự kiện lịch đã tạo
   */
  async create(tenantId: number, data: Partial<Calendar>): Promise<Calendar> {
    const event = this.repository.create({ ...data, tenantId });
    return this.repository.save(event);
  }

  /**
   * Cập nhật sự kiện lịch
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của sự kiện lịch
   * @param data Dữ liệu cần cập nhật
   * @returns Sự kiện lịch đã cập nhật
   */
  async update(tenantId: number, id: number, data: Partial<Calendar>): Promise<Calendar> {
    await this.repository.update({ id, tenantId }, data);
    const updatedEvent = await this.findById(tenantId, id);
    if (!updatedEvent) {
      throw new Error(`Calendar event with ID ${id} not found after update`);
    }
    return updatedEvent;
  }

  /**
   * Xóa sự kiện lịch
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của sự kiện lịch
   * @returns true nếu xóa thành công, false nếu không
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return result.affected ? result.affected > 0 : false;
  }

  /**
   * Xóa sự kiện lịch theo tham chiếu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param referenceType Loại tham chiếu
   * @param referenceId ID tham chiếu
   * @returns true nếu xóa thành công, false nếu không
   */
  async deleteByReference(
    tenantId: number,
    referenceType: ReferenceType,
    referenceId: number,
  ): Promise<boolean> {
    const result = await this.repository.delete({
      referenceType,
      referenceId,
      tenantId,
    });
    return result.affected ? result.affected > 0 : false;
  }
}
