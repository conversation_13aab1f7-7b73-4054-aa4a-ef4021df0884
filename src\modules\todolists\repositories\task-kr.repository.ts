import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TaskKr } from '../entities/task-kr.entity';

/**
 * Repository cho entity TaskKr
 */
@Injectable()
export class TaskKrRepository {
  constructor(
    @InjectRepository(TaskKr)
    private readonly repository: Repository<TaskKr>,
  ) {}

  /**
   * Tạo liên kết giữa task và key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu liên kết
   * @returns Liên kết đã tạo
   */
  async create(tenantId: number, data: Partial<TaskKr>): Promise<TaskKr> {
    const taskKr = this.repository.create({ ...data, tenantId });
    return this.repository.save(taskKr);
  }

  /**
   * Tạo nhiều liên kết giữa task và key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dataArray Mảng dữ liệu liên kết
   * @returns Mảng liên kết đã tạo
   */
  async createMany(tenantId: number, dataArray: Partial<TaskKr>[]): Promise<TaskKr[]> {
    const taskKrs = this.repository.create(dataArray.map(data => ({ ...data, tenantId })));
    return this.repository.save(taskKrs);
  }

  /**
   * Tìm tất cả key result của một task
   * @param tenantId ID tenant (required for tenant isolation)
   * @param taskId ID của task
   * @returns Danh sách liên kết
   */
  async findByTaskId(tenantId: number, taskId: number): Promise<TaskKr[]> {
    return this.repository.find({
      where: { taskId, tenantId },
    });
  }

  /**
   * Tìm tất cả task của một key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param krId ID của key result
   * @returns Danh sách liên kết
   */
  async findByKrId(tenantId: number, krId: number): Promise<TaskKr[]> {
    return this.repository.find({
      where: { krId, tenantId },
    });
  }

  /**
   * Tìm liên kết giữa task và key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param taskId ID của task
   * @param krId ID của key result
   * @returns Liên kết hoặc null nếu không tìm thấy
   */
  async findByTaskIdAndKrId(
    tenantId: number,
    taskId: number,
    krId: number,
  ): Promise<TaskKr | null> {
    return this.repository.findOne({
      where: {
        taskId,
        krId,
        tenantId,
      },
    });
  }

  /**
   * Xóa liên kết giữa task và key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param taskId ID của task
   * @param krId ID của key result
   * @returns true nếu xóa thành công
   */
  async delete(tenantId: number, taskId: number, krId: number): Promise<boolean> {
    const result = await this.repository.delete({
      taskId,
      krId,
      tenantId,
    });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa tất cả liên kết của một task
   * @param tenantId ID tenant (required for tenant isolation)
   * @param taskId ID của task
   * @returns true nếu xóa thành công
   */
  async deleteByTaskId(tenantId: number, taskId: number): Promise<boolean> {
    const result = await this.repository.delete({
      taskId,
      tenantId,
    });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
