import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeService } from './employee.service';
import { EmployeeRepository } from '../repositories/employee.repository';
import { Employee } from '../entities/employee.entity';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { UpdateEmployeeStatusDto } from '../dto/update-employee-status.dto';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { EmploymentType } from '../enum/employment-type.enum';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';

describe('EmployeeService', () => {
  let service: EmployeeService;
  let repository: EmployeeRepository;

  const mockEmployeeRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByUserId: jest.fn(),
    findByEmployeeCode: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeService,
        {
          provide: EmployeeRepository,
          useValue: mockEmployeeRepository,
        },
      ],
    }).compile();

    service = module.get<EmployeeService>(EmployeeService);
    repository = module.get<EmployeeRepository>(EmployeeRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated employees', async () => {
      const mockQuery = { page: 1, limit: 10 };
      const mockResult = {
        items: [{ id: 1, employeeCode: 'EMP001' }] as Employee[],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      mockEmployeeRepository.findAll.mockResolvedValue(mockResult);

      const result = await service.findAll(mockQuery);

      expect(result).toEqual(mockResult);
      expect(mockEmployeeRepository.findAll).toHaveBeenCalledWith(mockQuery);
    });

    it('should throw an exception when repository throws an error', async () => {
      const mockQuery = { page: 1, limit: 10 };
      mockEmployeeRepository.findAll.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.findAll(mockQuery)).rejects.toThrow(AppException);
      expect(mockEmployeeRepository.findAll).toHaveBeenCalledWith(mockQuery);
    });
  });

  describe('findById', () => {
    it('should return an employee when found', async () => {
      const mockEmployee = { id: 1, employeeCode: 'EMP001' } as Employee;
      mockEmployeeRepository.findById.mockResolvedValue(mockEmployee);

      const result = await service.findById(1);

      expect(result).toEqual(mockEmployee);
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
    });

    it('should throw an exception when employee not found', async () => {
      mockEmployeeRepository.findById.mockResolvedValue(null);

      await expect(service.findById(1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          'Employee with ID 1 not found',
        ),
      );
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
    });
  });

  describe('create', () => {
    it('should create and return a new employee', async () => {
      const mockCreateDto: CreateEmployeeDto = {
        userId: 1,
        employeeCode: 'EMP001',
      };
      const mockCreatedEmployee = {
        id: 1,
        userId: 1,
        employeeCode: 'EMP001',
        status: EmployeeStatus.ACTIVE,
      } as Employee;

      mockEmployeeRepository.findByEmployeeCode.mockResolvedValue(null);
      mockEmployeeRepository.create.mockResolvedValue(mockCreatedEmployee);

      const result = await service.create(mockCreateDto, 1);

      expect(result).toEqual(mockCreatedEmployee);
      expect(mockEmployeeRepository.findByEmployeeCode).toHaveBeenCalledWith(
        'EMP001',
      );
      expect(mockEmployeeRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 1,
          employeeCode: 'EMP001',
          status: EmployeeStatus.ACTIVE,
          createdBy: 1,
          updatedBy: 1,
        }),
      );
    });

    it('should throw an exception when employee code already exists', async () => {
      const mockCreateDto: CreateEmployeeDto = {
        userId: 1,
        employeeCode: 'EMP001',
      };
      const existingEmployee = { id: 2, employeeCode: 'EMP001' } as Employee;

      mockEmployeeRepository.findByEmployeeCode.mockResolvedValue(
        existingEmployee,
      );

      await expect(service.create(mockCreateDto, 1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.EMPLOYEE_CODE_EXISTS,
          'Employee with code EMP001 already exists',
        ),
      );
      expect(mockEmployeeRepository.findByEmployeeCode).toHaveBeenCalledWith(
        'EMP001',
      );
      expect(mockEmployeeRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update and return the employee', async () => {
      const mockEmployee = {
        id: 1,
        employeeCode: 'EMP001',
        jobTitle: 'Developer',
      } as Employee;
      const mockUpdateDto: UpdateEmployeeDto = {
        jobTitle: 'Senior Developer',
      };
      const mockUpdatedEmployee = {
        id: 1,
        employeeCode: 'EMP001',
        jobTitle: 'Senior Developer',
      } as Employee;

      mockEmployeeRepository.findById.mockResolvedValue(mockEmployee);
      mockEmployeeRepository.update.mockResolvedValue(mockUpdatedEmployee);

      const result = await service.update(1, mockUpdateDto, 1);

      expect(result).toEqual(mockUpdatedEmployee);
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          jobTitle: 'Senior Developer',
          updatedBy: 1,
        }),
      );
    });

    it('should throw an exception when employee not found', async () => {
      const mockUpdateDto: UpdateEmployeeDto = {
        jobTitle: 'Senior Developer',
      };

      mockEmployeeRepository.findById.mockResolvedValue(null);

      await expect(service.update(1, mockUpdateDto, 1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          'Employee with ID 1 not found',
        ),
      );
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('updateStatus', () => {
    it('should update employee status', async () => {
      const mockEmployee = {
        id: 1,
        employeeCode: 'EMP001',
        status: EmployeeStatus.ACTIVE,
      } as Employee;
      const mockUpdateStatusDto: UpdateEmployeeStatusDto = {
        status: EmployeeStatus.ON_LEAVE,
        reason: 'Vacation',
      };
      const mockUpdatedEmployee = {
        id: 1,
        employeeCode: 'EMP001',
        status: EmployeeStatus.ON_LEAVE,
      } as Employee;

      mockEmployeeRepository.findById.mockResolvedValue(mockEmployee);
      mockEmployeeRepository.update.mockResolvedValue(mockUpdatedEmployee);

      const result = await service.updateStatus(1, mockUpdateStatusDto, 1);

      expect(result).toEqual(mockUpdatedEmployee);
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          status: EmployeeStatus.ON_LEAVE,
          updatedBy: 1,
        }),
      );
    });

    it('should require termination reason when status is TERMINATED', async () => {
      const mockEmployee = {
        id: 1,
        employeeCode: 'EMP001',
        status: EmployeeStatus.ACTIVE,
      } as Employee;
      const mockUpdateStatusDto: UpdateEmployeeStatusDto = {
        status: EmployeeStatus.TERMINATED,
      };

      mockEmployeeRepository.findById.mockResolvedValue(mockEmployee);

      await expect(
        service.updateStatus(1, mockUpdateStatusDto, 1),
      ).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.EMPLOYEE_TERMINATION_REASON_REQUIRED,
          'Termination reason is required',
        ),
      );
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete an employee and return true', async () => {
      const mockEmployee = { id: 1, employeeCode: 'EMP001' } as Employee;

      mockEmployeeRepository.findById.mockResolvedValue(mockEmployee);
      mockEmployeeRepository.delete.mockResolvedValue(true);

      const result = await service.delete(1);

      expect(result).toBe(true);
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.delete).toHaveBeenCalledWith(1);
    });

    it('should throw an exception when employee not found', async () => {
      mockEmployeeRepository.findById.mockResolvedValue(null);

      await expect(service.delete(1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          'Employee with ID 1 not found',
        ),
      );
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.delete).not.toHaveBeenCalled();
    });

    it('should throw an exception when delete fails', async () => {
      const mockEmployee = { id: 1, employeeCode: 'EMP001' } as Employee;

      mockEmployeeRepository.findById.mockResolvedValue(mockEmployee);
      mockEmployeeRepository.delete.mockResolvedValue(false);

      await expect(service.delete(1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.EMPLOYEE_DELETE_FAILED,
          'Failed to delete employee with ID 1',
        ),
      );
      expect(mockEmployeeRepository.findById).toHaveBeenCalledWith(1);
      expect(mockEmployeeRepository.delete).toHaveBeenCalledWith(1);
    });
  });
});
