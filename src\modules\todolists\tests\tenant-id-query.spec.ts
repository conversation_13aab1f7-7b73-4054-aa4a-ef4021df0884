import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Todo } from '../entities/todo.entity';
import { tenantContext } from '@/common/subscribers/tenant-entity.subscriber';
import { TodoRepository } from '../repositories/todo.repository';

/**
 * Test kiểm tra việc truyền tenantId vào truy vấn
 */
describe('TenantId Query Test', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let todoRepository: TodoRepository;
  let nativeRepository: Repository<Todo>;
  const logger = new Logger('TenantIdQueryTest');

  beforeAll(async () => {
    // Tạo module test
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = {
              host: configService.get<string>('DB_HOST', 'localhost'),
              port: parseInt(configService.get<string>('DB_PORT', '5432'), 10),
              username: configService.get<string>('DB_USERNAME', 'postgres'),
              password: configService.get<string>('DB_PASSWORD', 'postgres'),
              database: configService.get<string>('DB_DATABASE', 'postgres'),
              ssl: configService.get<string>('DB_SSL') === 'true',
            };

            return {
              type: 'postgres',
              host: dbConfig.host,
              port: dbConfig.port,
              username: dbConfig.username,
              password: dbConfig.password,
              database: dbConfig.database,
              entities: [Todo],
              synchronize: false,
              ssl: {
                rejectUnauthorized: !dbConfig.ssl,
              },
            };
          },
        }),
        TypeOrmModule.forFeature([Todo]),
      ],
      providers: [TodoRepository],
    }).compile();

    // Lấy các instance cần thiết
    dataSource = module.get<DataSource>(DataSource);
    todoRepository = module.get<TodoRepository>(TodoRepository);
    nativeRepository = module.get<Repository<Todo>>(getRepositoryToken(Todo));

    logger.log('Test module initialized');
  });

  afterAll(async () => {
    // Đóng kết nối sau khi test hoàn thành
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should connect to the database successfully', async () => {
    // Kiểm tra kết nối đã được thiết lập
    expect(dataSource.isInitialized).toBe(true);

    // Thử thực hiện một truy vấn đơn giản
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
    logger.log('Database connection successful');
  });

  it('should include tenantId in query when using repository', async () => {
    // Thiết lập tenantId trong context
    const testTenantId = 1;

    // Spy vào phương thức createQueryBuilder của repository
    const queryBuilderSpy = jest.spyOn(nativeRepository, 'createQueryBuilder');
    const andWhereSpy = jest.fn().mockReturnThis();
    const skipSpy = jest.fn().mockReturnThis();
    const takeSpy = jest.fn().mockReturnThis();
    const orderBySpy = jest.fn().mockReturnThis();
    const getManyAndCountSpy = jest.fn().mockResolvedValue([[], 0]);

    // Mock queryBuilder
    const mockQueryBuilder = {
      andWhere: andWhereSpy,
      skip: skipSpy,
      take: takeSpy,
      orderBy: orderBySpy,
      getManyAndCount: getManyAndCountSpy,
      expressionMap: {
        mainAlias: {
          name: 'todo',
        },
        wheres: [],
      },
    } as unknown as SelectQueryBuilder<Todo>;

    queryBuilderSpy.mockReturnValue(mockQueryBuilder);

    // Thực hiện truy vấn trong context với tenantId
    await tenantContext.run({ tenantId: testTenantId }, async () => {
      await todoRepository.findAll({
        page: 1,
        limit: 10,
      });

      // Kiểm tra xem andWhere có được gọi với tenantId không
      expect(andWhereSpy).toHaveBeenCalled();

      // Kiểm tra các tham số của andWhere
      const andWhereArgs = andWhereSpy.mock.calls.find(
        (call) => typeof call[0] === 'string' && call[0].includes('tenantId'),
      );

      if (andWhereArgs) {
        logger.log(`andWhere called with: ${andWhereArgs[0]}`);
        logger.log(`andWhere parameters: ${JSON.stringify(andWhereArgs[1])}`);

        // Kiểm tra xem tenantId có được truyền vào không
        expect(andWhereArgs[0]).toContain('todo.tenantId = :tenantId');
        expect(andWhereArgs[1]).toEqual({ tenantId: testTenantId });
      } else {
        fail('tenantId condition not found in query');
      }
    });
  });

  it('should not include tenantId in query when disableTenantFilter is true', async () => {
    // Thiết lập tenantId trong context với disableTenantFilter = true
    const testTenantId = 1;

    // Spy vào phương thức createQueryBuilder của repository
    const queryBuilderSpy = jest.spyOn(nativeRepository, 'createQueryBuilder');
    const andWhereSpy = jest.fn().mockReturnThis();
    const skipSpy = jest.fn().mockReturnThis();
    const takeSpy = jest.fn().mockReturnThis();
    const orderBySpy = jest.fn().mockReturnThis();
    const getManyAndCountSpy = jest.fn().mockResolvedValue([[], 0]);

    // Mock queryBuilder
    const mockQueryBuilder = {
      andWhere: andWhereSpy,
      skip: skipSpy,
      take: takeSpy,
      orderBy: orderBySpy,
      getManyAndCount: getManyAndCountSpy,
      expressionMap: {
        mainAlias: {
          name: 'todo',
        },
        wheres: [],
      },
    } as unknown as SelectQueryBuilder<Todo>;

    queryBuilderSpy.mockReturnValue(mockQueryBuilder);

    // Thực hiện truy vấn trong context với disableTenantFilter = true
    await tenantContext.run(
      { tenantId: testTenantId, disableTenantFilter: true },
      async () => {
        await todoRepository.findAll({
          page: 1,
          limit: 10,
        });

        // Kiểm tra các tham số của andWhere
        const tenantIdCondition = andWhereSpy.mock.calls.find(
          (call) => typeof call[0] === 'string' && call[0].includes('tenantId'),
        );

        // Không nên có điều kiện tenantId
        expect(tenantIdCondition).toBeUndefined();
        logger.log(
          'No tenantId condition found in query when disableTenantFilter is true',
        );
      },
    );
  });
});
