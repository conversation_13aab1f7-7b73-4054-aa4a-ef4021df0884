import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoComment } from '../entities/todo-comment.entity';
import { TodoCommentQueryDto } from '../dto/todo-comment/todo-comment-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository xử lý truy vấn dữ liệu cho bình luận công việc
 */
@Injectable()
export class TodoCommentRepository {
  constructor(
    @InjectRepository(TodoComment)
    private readonly repository: Repository<TodoComment>,
  ) {}

  /**
   * Tạo mới bình luận
   * @param data Dữ liệu bình luận
   * @returns Bình luận đã tạo
   */
  async create(data: Partial<TodoComment>): Promise<TodoComment> {
    const comment = this.repository.create(data);
    return this.repository.save(comment);
  }

  /**
   * Tìm bình luận theo ID
   * @param id ID bình luận
   * @returns Bình luận nếu tìm thấy, null nếu không
   */
  async findById(id: number): Promise<TodoComment | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tất cả bình luận với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách bình luận đã phân trang
   */
  async findAll(
    query: TodoCommentQueryDto,
  ): Promise<PaginatedResult<TodoComment>> {
    const {
      page = 1,
      limit = 10,
      todoId,
      userId,
      parentId,
      commentType,
      isSystemEvent,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('comment');

    // Áp dụng bộ lọc todoId nếu được cung cấp
    if (todoId) {
      queryBuilder.andWhere('comment.todoId = :todoId', { todoId });
    }

    // Áp dụng bộ lọc userId nếu được cung cấp
    if (userId) {
      queryBuilder.andWhere('comment.userId = :userId', { userId });
    }

    // Áp dụng bộ lọc parentId nếu được cung cấp
    if (parentId !== undefined) {
      queryBuilder.andWhere('comment.parentId = :parentId', { parentId });
    }

    // Áp dụng bộ lọc commentType nếu được cung cấp
    if (commentType) {
      queryBuilder.andWhere('comment.commentType = :commentType', {
        commentType,
      });
    }

    // Áp dụng bộ lọc isSystemEvent nếu được cung cấp
    if (isSystemEvent !== undefined) {
      queryBuilder.andWhere('comment.isSystemEvent = :isSystemEvent', {
        isSystemEvent,
      });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere('comment.contentHtml ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`comment.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm tất cả bình luận của một công việc
   * @param todoId ID công việc
   * @returns Danh sách bình luận
   */
  async findByTodoId(todoId: number): Promise<TodoComment[]> {
    return this.repository.find({
      where: { todoId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm tất cả bình luận con của một bình luận
   * @param parentId ID bình luận cha
   * @returns Danh sách bình luận con
   */
  async findReplies(parentId: number): Promise<TodoComment[]> {
    return this.repository.find({
      where: { parentId },
      order: { createdAt: 'ASC' },
    });
  }

  /**
   * Xóa bình luận
   * @param id ID bình luận
   * @returns Kết quả xóa
   */
  async remove(id: number): Promise<void> {
    await this.repository.delete(id);
  }
}
