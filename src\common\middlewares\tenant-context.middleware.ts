import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { tenantContext } from '../subscribers/tenant-entity.subscriber';
import { RequestWithUser } from './tenant-security.middleware';

/**
 * Middleware để thiết lập tenantId vào AsyncLocalStorage
 * Middleware này sẽ lấy tenantId từ request và lưu vào context
 * để TenantEntitySubscriber có thể sử dụng
 */
@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantContextMiddleware.name);

  /**
   * Xử lý request và thiết lập tenantId vào context
   * @param request Request từ client
   * @param response Response từ server
   * @param next Hàm next để chuyển tiếp request
   */
  use(request: RequestWithUser, response: Response, next: NextFunction): void {
    // Lấy tenantId từ request (đã đượ<PERSON> thiết lập bởi TenantSecurityMiddleware)
    let tenantId = request.tenantId;
    const userTenantId = request.user?.tenantId;
    const userType = request.user?.type;
    const userId = request.user?.id;

    this.logger.log(`[TenantContextMiddleware] Processing request for user ${userId} (type: ${userType})`);
    this.logger.log(`[TenantContextMiddleware] Request tenantId: ${tenantId}, User tenantId: ${userTenantId}`);

    // Nếu không có tenantId trong request, lấy từ user
    if (!tenantId && userTenantId) {
      tenantId = typeof userTenantId === 'string' ? parseInt(userTenantId, 10) : userTenantId;
      this.logger.log(`[TenantContextMiddleware] Using tenantId from user: ${tenantId}`);
    }

    if (tenantId) {
      // Đảm bảo tenantId là số
      if (typeof tenantId === 'string') {
        tenantId = parseInt(tenantId, 10);
        this.logger.debug(`[TenantContextMiddleware] Chuyển đổi tenantId từ chuỗi sang số: ${tenantId}`);
      }

      // Thiết lập tenantId vào context
      tenantContext.run({ tenantId }, () => {
        this.logger.log(`[TenantContextMiddleware] Thiết lập tenantId ${tenantId} vào context cho user ${userId}`);
        next();
      });
    } else {
      // Nếu không có tenantId, tiếp tục xử lý request mà không thiết lập context
      this.logger.warn(`[TenantContextMiddleware] Không có tenantId trong request cho user ${userId} (type: ${userType})`);
      next();
    }
  }
}
