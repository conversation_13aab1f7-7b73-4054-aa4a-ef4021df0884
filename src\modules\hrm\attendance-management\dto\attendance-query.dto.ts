import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON>ber, IsString, IsEnum, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';
import { AttendanceStatus } from '../enum/attendance-status.enum';

/**
 * DTO for querying attendance records
 */
export class AttendanceQueryDto {
  /**
   * Page number for pagination
   */
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  /**
   * Number of items per page
   */
  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  /**
   * Search term for filtering
   */
  @ApiProperty({
    description: 'Search term for filtering attendance records',
    example: 'John Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Sort field
   */
  @ApiProperty({
    description: 'Field to sort by',
    example: 'workDate',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'workDate';

  /**
   * Sort direction
   */
  @ApiProperty({
    description: 'Sort direction (ASC or DESC)',
    example: 'DESC',
    required: false,
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortDirection?: 'ASC' | 'DESC' = 'DESC';

  /**
   * Filter by employee ID
   */
  @ApiProperty({
    description: 'Filter by employee ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  employeeId?: number;

  /**
   * Filter by attendance status
   */
  @ApiProperty({
    description: 'Filter by attendance status',
    enum: AttendanceStatus,
    example: AttendanceStatus.PRESENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(AttendanceStatus)
  status?: AttendanceStatus;

  /**
   * Filter by work date from
   */
  @ApiProperty({
    description: 'Filter by work date from (YYYY-MM-DD)',
    example: '2023-12-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  workDateFrom?: string;

  /**
   * Filter by work date to
   */
  @ApiProperty({
    description: 'Filter by work date to (YYYY-MM-DD)',
    example: '2023-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  workDateTo?: string;

  /**
   * Filter by minimum work hours
   */
  @ApiProperty({
    description: 'Filter by minimum work hours (in minutes)',
    example: 480,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minWorkHours?: number;

  /**
   * Filter by maximum work hours
   */
  @ApiProperty({
    description: 'Filter by maximum work hours (in minutes)',
    example: 600,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  maxWorkHours?: number;

  /**
   * Filter by manual attendance records only
   */
  @ApiProperty({
    description: 'Filter by manual attendance records only',
    example: false,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  isManual?: boolean;
}
