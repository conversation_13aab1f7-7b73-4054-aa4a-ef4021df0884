import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TenantEntity } from '@/shared/database/subscribers/tenant-subscriber';

/**
 * Entity representing training courses and programs
 */
@Entity('trainings')
export class Training implements TenantEntity {
  /**
   * Unique identifier for the training
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Training course code
   */
  @Column({ name: 'course_code', type: 'varchar', length: 100, nullable: false, unique: true })
  courseCode: string;

  /**
   * Training title/name
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Training description
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Training category (technical, soft-skills, compliance, etc.)
   */
  @Column({ type: 'varchar', length: 100, nullable: false })
  category: string;

  /**
   * Training type (online, classroom, workshop, seminar)
   */
  @Column({ type: 'varchar', length: 50, nullable: false })
  type: string;

  /**
   * Training duration in hours
   */
  @Column({ name: 'duration_hours', type: 'integer', nullable: false })
  durationHours: number;

  /**
   * Training start date
   */
  @Column({ name: 'start_date', type: 'date', nullable: true })
  startDate: Date | null;

  /**
   * Training end date
   */
  @Column({ name: 'end_date', type: 'date', nullable: true })
  endDate: Date | null;

  /**
   * Maximum number of participants
   */
  @Column({ name: 'max_participants', type: 'integer', nullable: true })
  maxParticipants: number | null;

  /**
   * Current number of enrolled participants
   */
  @Column({ name: 'current_participants', type: 'integer', default: 0 })
  currentParticipants: number;

  /**
   * Training instructor/facilitator
   */
  @Column({ type: 'varchar', length: 255, nullable: true })
  instructor: string | null;

  /**
   * Training location (for classroom training)
   */
  @Column({ type: 'varchar', length: 255, nullable: true })
  location: string | null;

  /**
   * Training materials/resources
   */
  @Column({ type: 'text', nullable: true })
  materials: string | null;

  /**
   * Prerequisites for the training
   */
  @Column({ type: 'text', nullable: true })
  prerequisites: string | null;

  /**
   * Learning objectives
   */
  @Column({ name: 'learning_objectives', type: 'text', nullable: true })
  learningObjectives: string | null;

  /**
   * Training cost per participant
   */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  cost: number | null;

  /**
   * Currency for the cost
   */
  @Column({ type: 'varchar', length: 10, default: 'VND' })
  currency: string;

  /**
   * Training status (draft, active, completed, cancelled)
   */
  @Column({ type: 'varchar', length: 50, nullable: false, default: 'draft' })
  status: string;

  /**
   * Whether certification is provided
   */
  @Column({ name: 'provides_certification', type: 'boolean', default: false })
  providesCertification: boolean;

  /**
   * Certification validity period in months
   */
  @Column({ name: 'certification_validity_months', type: 'integer', nullable: true })
  certificationValidityMonths: number | null;

  /**
   * Training evaluation form/criteria
   */
  @Column({ name: 'evaluation_criteria', type: 'text', nullable: true })
  evaluationCriteria: string | null;

  /**
   * Minimum passing score for certification
   */
  @Column({ name: 'passing_score', type: 'decimal', precision: 5, scale: 2, nullable: true })
  passingScore: number | null;

  /**
   * External training provider
   */
  @Column({ name: 'external_provider', type: 'varchar', length: 255, nullable: true })
  externalProvider: string | null;

  /**
   * Training tags for categorization
   */
  @Column({ type: 'text', nullable: true })
  tags: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: false })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   * REQUIRED for tenant isolation
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
