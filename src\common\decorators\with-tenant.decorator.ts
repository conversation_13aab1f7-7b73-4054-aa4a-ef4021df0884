import { tenantContext } from '../subscribers/tenant-entity.subscriber';

/**
 * Decorator để thiết lập tenantId vào context cho một phương thức
 * Sử dụng decorator n<PERSON><PERSON> <PERSON>hi cần thực hiện các thao tác với database
 * trong một context không có request (ví dụ: trong các service, job, ...)
 *
 * @param tenantId ID của tenant
 * @returns Decorator
 *
 * @example
 * // Sử dụng với tenantId cố định
 * @WithTenant(1)
 * async someMethod() {
 *   // Các thao tác với database sẽ tự động thêm điều kiện tenantId = 1
 * }
 *
 * // Sử dụng với tenantId từ tham số
 * async someMethod(@Param('tenantId') tenantId: number) {
 *   await this.someServiceMethod(tenantId);
 * }
 *
 * @WithTenant((args) => args[0])
 * async someServiceMethod(tenantId: number) {
 *   // <PERSON><PERSON><PERSON> thao tác với database sẽ tự động thêm điều kiện tenantId = args[0]
 * }
 */
export function WithTenant(
  tenantIdOrResolver: number | ((methodArgs: any[]) => number),
): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      // Xác định tenantId
      const tenantId =
        typeof tenantIdOrResolver === 'function'
          ? tenantIdOrResolver(args)
          : tenantIdOrResolver;

      // Thực thi phương thức trong context với tenantId
      return tenantContext.run({ tenantId }, () => {
        return originalMethod.apply(this, args);
      });
    };

    return descriptor;
  };
}
