import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Objective } from '../entities/objective.entity';
import { ObjectiveQueryDto } from '../dto/objective/objective-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';


/**
 * Repository cho mục tiêu với tenant isolation đơn giản
 */
@Injectable()
export class ObjectiveRepository {
  private readonly logger = new Logger(ObjectiveRepository.name);

  constructor(
    @InjectRepository(Objective)
    private readonly repository: Repository<Objective>,
  ) {}

  /**
   * Tìm tất cả mục tiêu với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các mục tiêu
   */
  async findAll(tenantId: number, query: ObjectiveQueryDto): Promise<PaginatedResult<Objective>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      type,
      cycleId,
      ownerId,
      departmentId,
      parentId,
      startDate,
      endDate,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('objective');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('objective.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc nếu được cung cấp
    if (type) {
      queryBuilder.andWhere('objective.type = :type', { type });
    }

    if (cycleId) {
      queryBuilder.andWhere('objective.cycleId = :cycleId', { cycleId });
    }

    if (ownerId) {
      queryBuilder.andWhere('objective.ownerId = :ownerId', { ownerId });
    }

    if (departmentId) {
      queryBuilder.andWhere('objective.departmentId = :departmentId', {
        departmentId,
      });
    }

    if (parentId) {
      queryBuilder.andWhere('objective.parentId = :parentId', { parentId });
    } else if (parentId === null) {
      queryBuilder.andWhere('objective.parentId IS NULL');
    }

    // Áp dụng bộ lọc ngày bắt đầu nếu được cung cấp
    if (startDate) {
      queryBuilder.andWhere('objective.startDate >= :startDate', { startDate });
    }

    // Áp dụng bộ lọc ngày kết thúc nếu được cung cấp
    if (endDate) {
      queryBuilder.andWhere('objective.endDate <= :endDate', { endDate });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'objective.title ILIKE :search OR objective.description ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`objective.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm mục tiêu theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @returns Mục tiêu hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<Objective | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tạo mới mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu mục tiêu
   * @returns Mục tiêu đã tạo
   */
  async create(tenantId: number, data: Partial<Objective>): Promise<Objective> {
    const objective = this.repository.create({ ...data, tenantId });
    return this.repository.save(objective);
  }

  /**
   * Cập nhật mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @param data Dữ liệu mục tiêu cập nhật
   * @returns Mục tiêu đã cập nhật hoặc null nếu không tìm thấy
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Objective>,
  ): Promise<Objective | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Xóa mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @returns True nếu xóa thành công, false nếu không tìm thấy
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Tìm mục tiêu theo ID chu kỳ
   * @param tenantId ID tenant (required for tenant isolation)
   * @param cycleId ID chu kỳ OKR
   * @returns Danh sách mục tiêu
   */
  async findByCycleId(tenantId: number, cycleId: number): Promise<Objective[]> {
    return this.repository.find({
      where: { cycleId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm mục tiêu theo ID chủ sở hữu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ownerId ID chủ sở hữu
   * @returns Danh sách mục tiêu
   */
  async findByOwnerId(tenantId: number, ownerId: number): Promise<Objective[]> {
    return this.repository.find({
      where: { ownerId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm mục tiêu con theo ID mục tiêu cha
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID mục tiêu cha
   * @returns Danh sách mục tiêu con
   */
  async findByParentId(tenantId: number, parentId: number): Promise<Objective[]> {
    return this.repository.find({
      where: { parentId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Cập nhật tiến độ mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @param progress Giá trị tiến độ (0-100)
   * @returns Mục tiêu đã cập nhật hoặc null nếu không tìm thấy
   */
  async updateProgress(
    tenantId: number,
    id: number,
    progress: number,
  ): Promise<Objective | null> {
    await this.repository.update({ id, tenantId }, { progress, updatedAt: Date.now() });
    return this.findById(tenantId, id);
  }
}
