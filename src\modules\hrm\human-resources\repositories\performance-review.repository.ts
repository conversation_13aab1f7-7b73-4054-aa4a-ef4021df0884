import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { PerformanceReview } from '../entities/performance-review.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository for performance review management with tenant isolation
 */
@Injectable()
export class PerformanceReviewRepository {
  private readonly logger = new Logger(PerformanceReviewRepository.name);

  constructor(
    @InjectRepository(PerformanceReview)
    private readonly repository: Repository<PerformanceReview>,
  ) {}

  /**
   * Find all performance reviews with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of performance reviews
   */
  async findAll(tenantId: number, query: any): Promise<PaginatedResult<PerformanceReview>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'periodEnd',
      sortDirection = 'DESC',
      employeeId,
      reviewerId,
      status,
      periodStart,
      periodEnd,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('review');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('review.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (employeeId) {
      queryBuilder.andWhere('review.employeeId = :employeeId', { employeeId });
    }

    if (reviewerId) {
      queryBuilder.andWhere('review.reviewerId = :reviewerId', { reviewerId });
    }

    if (status) {
      queryBuilder.andWhere('review.status = :status', { status });
    }

    if (periodStart) {
      queryBuilder.andWhere('review.periodStart >= :periodStart', { periodStart });
    }

    if (periodEnd) {
      queryBuilder.andWhere('review.periodEnd <= :periodEnd', { periodEnd });
    }

    // Apply search if provided
    if (search) {
      queryBuilder.andWhere(
        '(review.title ILIKE :search OR review.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`review.${sortBy}`, sortDirection);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find performance review by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Performance review ID
   * @returns Performance review or null if not found
   */
  async findById(tenantId: number, id: number): Promise<PerformanceReview | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find performance reviews by employee ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @returns List of performance reviews
   */
  async findByEmployeeId(tenantId: number, employeeId: number): Promise<PerformanceReview[]> {
    return this.repository.find({
      where: { employeeId, tenantId },
      order: { periodEnd: 'DESC' },
    });
  }

  /**
   * Find performance reviews by reviewer ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param reviewerId Reviewer ID
   * @returns List of performance reviews
   */
  async findByReviewerId(tenantId: number, reviewerId: number): Promise<PerformanceReview[]> {
    return this.repository.find({
      where: { reviewerId, tenantId },
      order: { periodEnd: 'DESC' },
    });
  }

  /**
   * Find performance reviews within date range
   * @param tenantId ID tenant (required for tenant isolation)
   * @param startDate Start date
   * @param endDate End date
   * @param employeeId Employee ID (optional)
   * @returns List of performance reviews
   */
  async findByDateRange(
    tenantId: number,
    startDate: Date,
    endDate: Date,
    employeeId?: number
  ): Promise<PerformanceReview[]> {
    const whereCondition: any = {
      tenantId,
      periodEnd: Between(startDate, endDate),
    };

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    return this.repository.find({
      where: whereCondition,
      order: { periodEnd: 'ASC' },
    });
  }

  /**
   * Find overdue performance reviews
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns List of overdue performance reviews
   */
  async findOverdueReviews(tenantId: number): Promise<PerformanceReview[]> {
    const today = new Date();
    
    return this.repository.find({
      where: {
        tenantId,
        status: 'draft',
        dueDate: Between(new Date('1900-01-01'), today),
      },
      order: { dueDate: 'ASC' },
    });
  }

  /**
   * Create a new performance review
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Performance review data
   * @returns Created performance review
   */
  async create(tenantId: number, data: Partial<PerformanceReview>): Promise<PerformanceReview> {
    const review = this.repository.create({ ...data, tenantId });
    return this.repository.save(review);
  }

  /**
   * Update performance review
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Performance review ID
   * @param data Updated performance review data
   * @returns Updated performance review or null if not found
   */
  async update(tenantId: number, id: number, data: Partial<PerformanceReview>): Promise<PerformanceReview | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete performance review
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Performance review ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Get performance review statistics for employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeId Employee ID
   * @param startDate Start date
   * @param endDate End date
   * @returns Performance statistics
   */
  async getPerformanceStats(
    tenantId: number,
    employeeId: number,
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalReviews: number;
    averageOverallRating: number;
    averageTechnicalRating: number;
    averageCommunicationRating: number;
    averageLeadershipRating: number;
    averageTeamworkRating: number;
    completedReviews: number;
    pendingReviews: number;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('review');

    queryBuilder
      .select([
        'COUNT(*) as totalReviews',
        'AVG(COALESCE(review.overallRating, 0)) as averageOverallRating',
        'AVG(COALESCE(review.technicalRating, 0)) as averageTechnicalRating',
        'AVG(COALESCE(review.communicationRating, 0)) as averageCommunicationRating',
        'AVG(COALESCE(review.leadershipRating, 0)) as averageLeadershipRating',
        'AVG(COALESCE(review.teamworkRating, 0)) as averageTeamworkRating',
        'SUM(CASE WHEN review.status = :completedStatus THEN 1 ELSE 0 END) as completedReviews',
        'SUM(CASE WHEN review.status IN (:...pendingStatuses) THEN 1 ELSE 0 END) as pendingReviews',
      ])
      .where('review.tenantId = :tenantId', { tenantId })
      .andWhere('review.employeeId = :employeeId', { employeeId })
      .andWhere('review.periodEnd BETWEEN :startDate AND :endDate', { startDate, endDate })
      .setParameters({
        completedStatus: 'completed',
        pendingStatuses: ['draft', 'submitted', 'in_review'],
      });

    const result = await queryBuilder.getRawOne();

    return {
      totalReviews: parseInt(result.totalReviews) || 0,
      averageOverallRating: parseFloat(result.averageOverallRating) || 0,
      averageTechnicalRating: parseFloat(result.averageTechnicalRating) || 0,
      averageCommunicationRating: parseFloat(result.averageCommunicationRating) || 0,
      averageLeadershipRating: parseFloat(result.averageLeadershipRating) || 0,
      averageTeamworkRating: parseFloat(result.averageTeamworkRating) || 0,
      completedReviews: parseInt(result.completedReviews) || 0,
      pendingReviews: parseInt(result.pendingReviews) || 0,
    };
  }
}
