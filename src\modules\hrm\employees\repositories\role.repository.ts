import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Role } from '@/modules/auth/entities/role.entity';
import { RolePermission } from '@/modules/auth/entities/role-permission.entity';
import { Permission } from '@/modules/auth/entities/permission.entity';
import { UserRole } from '@/modules/auth/entities/user-role.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { RoleQueryDto } from '../dto/role/role-list.dto';

/**
 * Repository cho Role trong module HRM
 */
@Injectable()
export class RoleRepository {
  private readonly logger = new Logger(RoleRepository.name);

  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(RolePermission)
    private readonly rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
  ) {}

  /**
   * Lấy danh sách vai trò với phân trang và tìm kiếm
   * @param query Tham số truy vấn
   * @returns Danh sách vai trò phân trang
   */
  async findAll(query: RoleQueryDto): Promise<PaginatedResult<Role>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        name,
        sortBy = 'id',
        sortDirection = 'ASC',
      } = query;

      // Tạo query builder
      const queryBuilder = this.roleRepository.createQueryBuilder('role');

      // Áp dụng điều kiện tìm kiếm
      if (search) {
        queryBuilder.andWhere(
          'role.name ILIKE :search OR role.description ILIKE :search',
          { search: `%${search}%` },
        );
      }

      // Tìm kiếm theo tên nếu có
      if (name) {
        queryBuilder.andWhere('role.name ILIKE :name', { name: `%${name}%` });
      }

      // Áp dụng sắp xếp
      queryBuilder.orderBy(`role.${sortBy}`, sortDirection);

      // Áp dụng phân trang
      const [items, totalItems] = await queryBuilder
        .skip((page - 1) * limit)
        .take(limit)
        .getManyAndCount();

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding roles: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm vai trò theo ID
   * @param id ID của vai trò
   * @returns Vai trò hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<Role | null> {
    return this.roleRepository.findOne({
      where: { id },
    });
  }

  /**
   * Lấy danh sách quyền của vai trò
   * @param roleId ID của vai trò
   * @returns Danh sách quyền của vai trò
   */
  async getRolePermissions(
    roleId: number,
  ): Promise<{ permissionIds: number[]; permissions: string[] }> {
    try {
      // Lấy danh sách permission của role
      const rolePermissions = await this.rolePermissionRepository.find({
        where: { roleId },
      });

      if (!rolePermissions || rolePermissions.length === 0) {
        return { permissionIds: [], permissions: [] };
      }

      const permissionIds = rolePermissions
        .map((rolePermission) => rolePermission.permissionId)
        .filter(Boolean) as number[];

      if (permissionIds.length === 0) {
        return { permissionIds: [], permissions: [] };
      }

      // Lấy thông tin chi tiết của các permission
      const permissions = await this.permissionRepository.find({
        where: { id: In(permissionIds) },
      });

      // Chuyển đổi thành định dạng 'module:action'
      const permissionStrings = permissions.map(
        (permission) => `${permission.module}:${permission.action}`,
      );

      return {
        permissionIds,
        permissions: permissionStrings,
      };
    } catch (error) {
      this.logger.error(
        `Error getting role permissions: ${error.message}`,
        error.stack,
      );
      return { permissionIds: [], permissions: [] };
    }
  }

  /**
   * Lấy danh sách vai trò của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách vai trò của người dùng
   */
  async getUserRoles(userId: number): Promise<Role[]> {
    try {
      // Lấy danh sách user_role của người dùng
      const userRoles = await this.userRoleRepository.find({
        where: { userId },
      });

      if (!userRoles || userRoles.length === 0) {
        return [];
      }

      const roleIds = userRoles
        .map((userRole) => userRole.roleId)
        .filter(Boolean) as number[];

      if (roleIds.length === 0) {
        return [];
      }

      // Lấy thông tin chi tiết của các vai trò
      const roles = await this.roleRepository.find({
        where: { id: In(roleIds) },
      });

      return roles;
    } catch (error) {
      this.logger.error(
        `Error getting user roles: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }
}
