import { Logger } from '@nestjs/common';
import { enableTenantDebugLogging } from './enable-debug-logging';
import { debugTenantSystem } from './tenant-debug';
import { testApiWithTenant } from './api-tenant-test';

/**
 * <PERSON><PERSON>t tổng hợp để chạy tất cả các test debug cho tenant system
 * Chạy: npm run debug:tenant-all
 */
async function runAllTenantDebug() {
  const logger = new Logger('TenantDebugRunner');
  
  try {
    // Enable debug logging
    enableTenantDebugLogging();
    
    logger.log('🚀 Starting comprehensive tenant debug session...');
    logger.log('='.repeat(60));
    
    // Test 1: Debug tenant context system
    logger.log('\n📋 Step 1: Testing tenant context system...');
    await debugTenantSystem();
    
    logger.log('\n⏳ Waiting 2 seconds before API tests...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 2: Debug API with tenant isolation
    logger.log('\n🌐 Step 2: Testing API with tenant isolation...');
    await testApiWithTenant();
    
    logger.log('\n✅ All tenant debug tests completed successfully!');
    logger.log('='.repeat(60));
    
    // Tóm tắt kết quả
    logger.log('\n📊 Debug Summary:');
    logger.log('- Tenant context system: ✅ Tested');
    logger.log('- API tenant isolation: ✅ Tested');
    logger.log('- Middleware logging: ✅ Enabled');
    logger.log('- Subscriber logging: ✅ Enabled');
    
    logger.log('\n💡 Next steps:');
    logger.log('1. Check the logs above for any tenant isolation issues');
    logger.log('2. Look for [TENANT-DEBUG], [TENANT-WARNING], [TENANT-ERROR] messages');
    logger.log('3. Verify that tenantId is correctly injected in all queries');
    logger.log('4. Check that users can only see data from their own tenant');
    
  } catch (error) {
    logger.error('❌ Tenant debug session failed:', error.message);
    logger.error(error.stack);
    throw error;
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  runAllTenantDebug()
    .then(() => {
      console.log('\n🎉 Tenant debug session completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Tenant debug session failed:', error);
      process.exit(1);
    });
}

export { runAllTenantDebug };
