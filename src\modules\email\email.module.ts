import { Global, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

import { PlaceholderInjectorService } from './services/placeholder-injector.service';
import { EmailService } from './services/email.service';
import { EmailPlaceholderService } from './services/email-placeholder.service';
import { SystemEmailService } from './services/system-email.service';
import { EmailDebugController } from './controllers/email-debug.controller';

import { QueueModule } from '@shared/queue/queue.module';
import { EmailClientService } from './services/email-client.service';

/**
 * Module quản lý email
 */
@Global()
@Module({
  imports: [HttpModule, QueueModule],
  controllers: [EmailDebugController],
  providers: [
    PlaceholderInjectorService,
    EmailService,
    EmailPlaceholderService,
    SystemEmailService,
    EmailClientService, // Thêm EmailClientService vào providers
  ],
  exports: [
    PlaceholderInjectorService,
    EmailService,
    EmailPlaceholderService,
    SystemEmailService,
    EmailClientService, // Thêm EmailClientService vào exports
  ],
})
export class EmailModule {}
