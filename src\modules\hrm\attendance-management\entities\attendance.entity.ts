import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TenantEntity } from '@/shared/database/subscribers/tenant-subscriber';

/**
 * Entity representing employee attendance records
 */
@Entity('attendances')
export class Attendance implements TenantEntity {
  /**
   * Unique identifier for the attendance record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the employee
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: false })
  employeeId: number;

  /**
   * Work date (YYYY-MM-DD format)
   */
  @Column({ name: 'work_date', type: 'date', nullable: false })
  workDate: Date;

  /**
   * Check-in time (timestamp in milliseconds)
   */
  @Column({ name: 'check_in_time', type: 'bigint', nullable: true })
  checkInTime: number | null;

  /**
   * Check-out time (timestamp in milliseconds)
   */
  @Column({ name: 'check_out_time', type: 'bigint', nullable: true })
  checkOutTime: number | null;

  /**
   * Total work hours for the day (in minutes)
   */
  @Column({ name: 'work_hours', type: 'integer', nullable: true })
  workHours: number | null;

  /**
   * Break time duration (in minutes)
   */
  @Column({ name: 'break_time', type: 'integer', nullable: true, default: 0 })
  breakTime: number | null;

  /**
   * Overtime hours (in minutes)
   */
  @Column({ name: 'overtime_hours', type: 'integer', nullable: true, default: 0 })
  overtimeHours: number | null;

  /**
   * Attendance status (present, absent, late, early_leave, etc.)
   */
  @Column({ type: 'varchar', length: 50, nullable: false, default: 'present' })
  status: string;

  /**
   * Check-in location (GPS coordinates or office location)
   */
  @Column({ name: 'check_in_location', type: 'varchar', length: 255, nullable: true })
  checkInLocation: string | null;

  /**
   * Check-out location (GPS coordinates or office location)
   */
  @Column({ name: 'check_out_location', type: 'varchar', length: 255, nullable: true })
  checkOutLocation: string | null;

  /**
   * Check-in IP address
   */
  @Column({ name: 'check_in_ip', type: 'varchar', length: 45, nullable: true })
  checkInIp: string | null;

  /**
   * Check-out IP address
   */
  @Column({ name: 'check_out_ip', type: 'varchar', length: 45, nullable: true })
  checkOutIp: string | null;

  /**
   * Notes or comments about the attendance
   */
  @Column({ type: 'text', nullable: true })
  notes: string | null;

  /**
   * Whether the attendance was manually adjusted by admin
   */
  @Column({ name: 'is_manual', type: 'boolean', default: false })
  isManual: boolean;

  /**
   * ID of the admin who made manual adjustments
   */
  @Column({ name: 'adjusted_by', type: 'integer', nullable: true })
  adjustedBy: number | null;

  /**
   * Timestamp when manual adjustment was made
   */
  @Column({ name: 'adjusted_at', type: 'bigint', nullable: true })
  adjustedAt: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: false })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   * REQUIRED for tenant isolation
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
