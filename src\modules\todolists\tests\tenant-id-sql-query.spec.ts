import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Todo } from '../entities/todo.entity';
import { tenantContext } from '@/common/subscribers/tenant-entity.subscriber';
import { getRepositoryToken } from '@nestjs/typeorm';

/**
 * Test kiểm tra SQL query được tạo ra với tenantId
 */
describe('TenantId SQL Query Test', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let todoRepository: Repository<Todo>;
  const logger = new Logger('TenantIdSqlQueryTest');

  beforeAll(async () => {
    // Tạo module test
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = {
              host: configService.get<string>('DB_HOST', 'localhost'),
              port: parseInt(configService.get<string>('DB_PORT', '5432'), 10),
              username: configService.get<string>('DB_USERNAME', 'postgres'),
              password: configService.get<string>('DB_PASSWORD', 'postgres'),
              database: configService.get<string>('DB_DATABASE', 'postgres'),
              ssl: configService.get<string>('DB_SSL') === 'true',
            };

            return {
              type: 'postgres',
              host: dbConfig.host,
              port: dbConfig.port,
              username: dbConfig.username,
              password: dbConfig.password,
              database: dbConfig.database,
              entities: [Todo],
              synchronize: false,
              ssl: {
                rejectUnauthorized: !dbConfig.ssl,
              },
              logging: ['query', 'error'],
              logger: 'advanced-console',
            };
          },
        }),
        TypeOrmModule.forFeature([Todo]),
      ],
    }).compile();

    // Lấy các instance cần thiết
    dataSource = module.get<DataSource>(DataSource);
    todoRepository = module.get<Repository<Todo>>(getRepositoryToken(Todo));

    logger.log('Test module initialized');
  });

  afterAll(async () => {
    // Đóng kết nối sau khi test hoàn thành
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should connect to the database successfully', async () => {
    // Kiểm tra kết nối đã được thiết lập
    expect(dataSource.isInitialized).toBe(true);

    // Thử thực hiện một truy vấn đơn giản
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
    logger.log('Database connection successful');
  });

  it('should include tenantId in SQL query', async () => {
    // Thiết lập tenantId trong context
    const testTenantId = 1;

    // Tạo một logger tạm thời để bắt SQL query
    const loggedQueries: string[] = [];
    const originalLogger = dataSource.logger;

    // Override logger để bắt SQL query
    const mockLogger = {
      logQuery: (query: string) => {
        loggedQueries.push(query);
        logger.log(`SQL Query: ${query}`);
      },
      logQueryError: originalLogger.logQueryError,
      logQuerySlow: originalLogger.logQuerySlow,
      logSchemaBuild: originalLogger.logSchemaBuild,
      logMigration: originalLogger.logMigration,
      log: originalLogger.log,
    };

    // Gán logger mới
    (dataSource as any).logger = mockLogger;

    try {
      // Thực hiện truy vấn trong context với tenantId
      await tenantContext.run({ tenantId: testTenantId }, async () => {
        // Tạo một query builder và thực hiện truy vấn
        const queryBuilder = todoRepository.createQueryBuilder('todo');
        await queryBuilder.getMany();

        // Kiểm tra xem có SQL query nào được ghi log không
        expect(loggedQueries.length).toBeGreaterThan(0);

        // Kiểm tra xem SQL query có chứa điều kiện tenantId không
        const hasTenantIdCondition = loggedQueries.some((query) =>
          query.includes(`"todo"."tenant_id" = ${testTenantId}`),
        );

        expect(hasTenantIdCondition).toBe(true);
        logger.log('SQL query includes tenantId condition');
      });
    } finally {
      // Khôi phục logger gốc
      (dataSource as any).logger = originalLogger;
    }
  });

  it('should not include tenantId in SQL query when disableTenantFilter is true', async () => {
    // Thiết lập tenantId trong context với disableTenantFilter = true
    const testTenantId = 1;

    // Tạo một logger tạm thời để bắt SQL query
    const loggedQueries: string[] = [];
    const originalLogger = dataSource.logger;

    // Override logger để bắt SQL query
    const mockLogger = {
      logQuery: (query: string) => {
        loggedQueries.push(query);
        logger.log(`SQL Query: ${query}`);
      },
      logQueryError: originalLogger.logQueryError,
      logQuerySlow: originalLogger.logQuerySlow,
      logSchemaBuild: originalLogger.logSchemaBuild,
      logMigration: originalLogger.logMigration,
      log: originalLogger.log,
    };

    // Gán logger mới
    (dataSource as any).logger = mockLogger;

    try {
      // Thực hiện truy vấn trong context với disableTenantFilter = true
      await tenantContext.run(
        { tenantId: testTenantId, disableTenantFilter: true },
        async () => {
          // Tạo một query builder và thực hiện truy vấn
          const queryBuilder = todoRepository.createQueryBuilder('todo');
          await queryBuilder.getMany();

          // Kiểm tra xem có SQL query nào được ghi log không
          expect(loggedQueries.length).toBeGreaterThan(0);

          // Kiểm tra xem SQL query có chứa điều kiện tenantId không
          const hasTenantIdCondition = loggedQueries.some((query) =>
            query.includes(`"todo"."tenant_id" = ${testTenantId}`),
          );

          expect(hasTenantIdCondition).toBe(false);
          logger.log(
            'SQL query does not include tenantId condition when disableTenantFilter is true',
          );
        },
      );
    } finally {
      // Khôi phục logger gốc
      (dataSource as any).logger = originalLogger;
    }
  });
});
