import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';
import { EmailSystemQueueService } from './email-system-queue.service';
import { QueueName, DEFAULT_JOB_OPTIONS } from './queue.constants';
import { BullModule } from '@nestjs/bullmq';

/**
 * Module hàng đợi dùng chung cho toàn ứng dụng
 */
@Global()
@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URL');

        return {
          // Cấu hình kết nối Redis dựa trên URL thay vì các tham số riêng lẻ
          connection: {
            url: redisUrl,
          },
          defaultJobOptions: DEFAULT_JOB_OPTIONS,
        };
      },
    }),
    /**
     * Đăng ký các queue cụ thể ở đây
     * Mỗi queue là một module con trong hệ thống
     */
    BullModule.registerQueue(
      {
        name: QueueName.EMAIL, // Queue xử lý email
      },
      {
        name: QueueName.SMS, // Queue xử lý SMS
      },
      {
        name: QueueName.NOTIFICATION, // Queue xử lý thông báo
      },
      {
        name: QueueName.DATA_PROCESS, // Queue xử lý dữ liệu
      },
      {
        name: QueueName.SEND_SYSTEM_EMAIL, // Queue xử lý email hệ thống
      },
      {
        name: QueueName.AGENT, // Queue xử lý agent
      },
      {
        name: QueueName.EMAIL_SYSTEM, // Queue xử lý email system
      },
      {
        name: QueueName.EMAIL_MARKETING, // Queue xử lý email marketing
      },
      {
        name: QueueName.CRAWL_URL, // Queue xử lý crawl URL cho user
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
        },
      },
      {
        name: QueueName.CRAWL_URL_ADMIN, // Queue xử lý crawl URL cho admin
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
        },
      },
    ),
  ],
  providers: [QueueService, EmailSystemQueueService],
  exports: [QueueService, EmailSystemQueueService, BullModule],
})
export class QueueModule {}