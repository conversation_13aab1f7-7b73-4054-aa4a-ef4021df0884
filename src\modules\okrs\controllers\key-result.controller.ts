import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { KeyResultService } from '../services/key-result.service';
import {
  CreateKeyResultDto,
  UpdateKeyResultDto,
  KeyResultQueryDto,
  KeyResultResponseDto,
} from '../dto/key-result';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller cho kết quả chính
 */
@ApiTags(SWAGGER_API_TAG.OKRS)
@Controller('api/okrs/key-results')
@UseGuards(JwtUserGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class KeyResultController {
  constructor(private readonly keyResultService: KeyResultService) {}

  /**
   * Tạo mới kết quả chính
   * @param dto DTO tạo kết quả chính
   * @returns Phản hồi kết quả chính đã tạo
   */
  @Post()
  @RequirePermissionEnum(Permission.OKR_CREATE_KEY_RESULT)
  @ApiOperation({ summary: 'Tạo mới kết quả chính' })
  @ApiResponse({
    status: 201,
    description: 'Kết quả chính đã được tạo thành công.',
    type: ApiResponseDto,
  })
  async create(
    @Body() dto: CreateKeyResultDto,
  ): Promise<ApiResponseDto<KeyResultResponseDto>> {
    // Tạo kết quả chính và liên kết với các keyresult của objective cha nếu có
    // supportingKeyResultIds là danh sách ID của các keyresult mà key result này hỗ trợ
    const result = await this.keyResultService.create(dto);
    return ApiResponseDto.created(result);
  }

  /**
   * Lấy tất cả kết quả chính với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các phản hồi kết quả chính
   */
  @Get()
  @RequirePermissionEnum(Permission.OKR_VIEW_LIST)
  @ApiOperation({ summary: 'Lấy tất cả kết quả chính' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kết quả chính.',
    type: ApiResponseDto,
  })
  async findAll(
    @Query() query: KeyResultQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<KeyResultResponseDto>>> {
    const result = await this.keyResultService.findAll(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy kết quả chính theo ID
   * @param id ID kết quả chính
   * @returns Phản hồi kết quả chính
   */
  @Get(':id')
  @RequirePermissionEnum(Permission.OKR_VIEW_DETAIL)
  @ApiOperation({ summary: 'Lấy kết quả chính theo ID' })
  @ApiParam({ name: 'id', description: 'ID kết quả chính' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả chính.',
    type: ApiResponseDto,
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<KeyResultResponseDto>> {
    const result = await this.keyResultService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật kết quả chính
   * @param id ID kết quả chính
   * @param dto DTO cập nhật kết quả chính
   * @returns Phản hồi kết quả chính đã cập nhật
   */
  @Put(':id')
  @RequirePermissionEnum(Permission.OKR_UPDATE_KEY_RESULT)
  @ApiOperation({ summary: 'Cập nhật kết quả chính' })
  @ApiParam({ name: 'id', description: 'ID kết quả chính' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả chính đã được cập nhật thành công.',
    type: ApiResponseDto,
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateKeyResultDto,
  ): Promise<ApiResponseDto<KeyResultResponseDto>> {
    const result = await this.keyResultService.update(id, dto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa kết quả chính
   * @param id ID kết quả chính
   * @returns Thông báo thành công
   */
  @Delete(':id')
  @RequirePermissionEnum(Permission.OKR_DELETE_KEY_RESULT)
  @ApiOperation({ summary: 'Xóa kết quả chính' })
  @ApiParam({ name: 'id', description: 'ID kết quả chính' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả chính đã được xóa thành công.',
    type: ApiResponseDto,
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.keyResultService.delete(id);
    return ApiResponseDto.deleted(result);
  }

  /**
   * Lấy kết quả chính theo ID mục tiêu
   * @param objectiveId ID mục tiêu
   * @returns Danh sách phản hồi kết quả chính
   */
  @Get('by-objective/:objectiveId')
  @ApiOperation({ summary: 'Lấy kết quả chính theo ID mục tiêu' })
  @ApiParam({ name: 'objectiveId', description: 'ID mục tiêu' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kết quả chính cho mục tiêu.',
    type: ApiResponseDto,
  })
  async findByObjectiveId(
    @Param('objectiveId', ParseIntPipe) objectiveId: number,
  ): Promise<ApiResponseDto<KeyResultResponseDto[]>> {
    const result = await this.keyResultService.findByObjectiveId(objectiveId);
    return ApiResponseDto.success(result);
  }
}
