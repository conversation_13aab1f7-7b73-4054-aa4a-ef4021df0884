import { Injectable, Logger } from '@nestjs/common';
// import { ChatService } from './chat.service';
import { FacebookService } from './facebook.service';
import { AIOrchestatorService } from './ai-orchestrator.service';
import { ChatMessage } from '../entities/chat-message.entity';
import { ChatConversation } from '../entities/chat-conversation.entity';

/**
 * Interface cho Facebook Webhook Message
 */
interface FacebookWebhookMessage {
  sender: { id: string };
  recipient: { id: string };
  timestamp: number;
  message?: {
    mid: string;
    text?: string;
    attachments?: any[];
    quick_reply?: { payload: string };
  };
  postback?: {
    payload: string;
    title: string;
  };
}

/**
 * Service xử lý tin nhắn từ Facebook và điều phối AI
 */
@Injectable()
export class MessageProcessorService {
  private readonly logger = new Logger(MessageProcessorService.name);

  constructor(
    // private readonly chatService: ChatService,
    private readonly facebookService: FacebookService,
    private readonly aiOrchestrator: AIOrchestatorService,
  ) {}

  /**
   * Xử lý tin nhắn đến từ Facebook Webhook
   */
  async processIncomingMessage(
    pageId: string,
    pageAccessToken: string,
    webhookMessage: FacebookWebhookMessage,
    tenantId: number,
  ): Promise<void> {
    try {
      const senderId = webhookMessage.sender.id;
      
      // Tìm hoặc tạo cuộc hội thoại
      // TODO: Implement ChatService
      let conversation: ChatConversation = {
        id: 1,
        facebookUserId: senderId,
        pageId,
        userName: 'Test User',
        userAvatar: null,
        status: 'active',
        assignedAgentId: null,
        conversationType: 'auto',
        language: 'vi',
        metadata: null,
        lastMessageAt: null,
        createdAt: Date.now(),
        updatedAt: null,
        tenantId,
      };

      // Lưu tin nhắn đến
      const incomingMessage = await this.saveIncomingMessage(
        conversation,
        webhookMessage,
      );

      // Hiển thị typing indicator
      await this.facebookService.setTypingIndicator(
        pageAccessToken,
        senderId,
        'typing_on',
      );

      // Xử lý tin nhắn với AI
      const aiResponse = await this.aiOrchestrator.processMessage(
        conversation,
        incomingMessage,
        tenantId,
      );

      // Tắt typing indicator
      await this.facebookService.setTypingIndicator(
        pageAccessToken,
        senderId,
        'typing_off',
      );

      // Gửi phản hồi
      await this.sendAIResponse(
        pageAccessToken,
        senderId,
        aiResponse,
        conversation,
      );

    } catch (error) {
      this.logger.error(`Error processing message: ${error.message}`);
      
      // Gửi tin nhắn lỗi generic
      await this.facebookService.sendTextMessage(
        pageAccessToken,
        webhookMessage.sender.id,
        'Xin lỗi, tôi đang gặp sự cố kỹ thuật. Vui lòng thử lại sau.',
      );
    }
  }

  /**
   * Lưu tin nhắn đến vào database
   */
  private async saveIncomingMessage(
    conversation: ChatConversation,
    webhookMessage: FacebookWebhookMessage,
  ): Promise<ChatMessage> {
    // TODO: Implement ChatService.createMessage
    return {
      id: 1,
      conversationId: conversation.id,
      facebookMessageId: webhookMessage.message?.mid || null,
      messageType: this.determineMessageType(webhookMessage),
      content: this.extractMessageContent(webhookMessage),
      attachments: webhookMessage.message?.attachments || null,
      direction: 'incoming' as any,
      senderType: 'user' as any,
      senderAgentId: null,
      status: 'received',
      isAiGenerated: false,
      aiContext: null,
      aiConfidence: null,
      detectedIntent: null,
      extractedEntities: null,
      metadata: null,
      createdAt: webhookMessage.timestamp,
      updatedAt: null,
      tenantId: conversation.tenantId,
    };
  }

  /**
   * Xác định loại tin nhắn
   */
  private determineMessageType(webhookMessage: FacebookWebhookMessage): string {
    if (webhookMessage.postback) return 'postback';
    if (webhookMessage.message?.quick_reply) return 'quick_reply';
    if (webhookMessage.message?.attachments && webhookMessage.message.attachments.length > 0) {
      const attachment = webhookMessage.message.attachments[0];
      return attachment.type || 'attachment';
    }
    return 'text';
  }

  /**
   * Trích xuất nội dung tin nhắn
   */
  private extractMessageContent(webhookMessage: FacebookWebhookMessage): string {
    if (webhookMessage.postback) {
      return webhookMessage.postback.title;
    }
    if (webhookMessage.message?.quick_reply) {
      return webhookMessage.message.quick_reply.payload;
    }
    return webhookMessage.message?.text || '';
  }

  /**
   * Gửi phản hồi AI
   */
  private async sendAIResponse(
    pageAccessToken: string,
    recipientId: string,
    aiResponse: any,
    conversation: ChatConversation,
  ): Promise<void> {
    try {
      let facebookResponse;

      if (aiResponse.quickReplies && aiResponse.quickReplies.length > 0) {
        // Gửi tin nhắn với quick replies
        facebookResponse = await this.facebookService.sendQuickReplies(
          pageAccessToken,
          recipientId,
          aiResponse.text,
          aiResponse.quickReplies,
        );
      } else {
        // Gửi tin nhắn text thường
        facebookResponse = await this.facebookService.sendTextMessage(
          pageAccessToken,
          recipientId,
          aiResponse.text,
        );
      }

      // TODO: Implement ChatService.createMessage and updateConversationLastMessage
      this.logger.log(`AI response sent to ${recipientId}: ${aiResponse.text}`);

    } catch (error) {
      this.logger.error(`Error sending AI response: ${error.message}`);
      throw error;
    }
  }
}
