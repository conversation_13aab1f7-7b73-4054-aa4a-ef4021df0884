import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TenantEntity } from '@/shared/database/subscribers/tenant-subscriber';

/**
 * Entity representing employee training enrollment and completion records
 */
@Entity('employee_trainings')
export class EmployeeTraining implements TenantEntity {
  /**
   * Unique identifier for the employee training record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the employee
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: false })
  employeeId: number;

  /**
   * ID of the training course
   */
  @Column({ name: 'training_id', type: 'integer', nullable: false })
  trainingId: number;

  /**
   * Date when employee enrolled in training
   */
  @Column({ name: 'enrollment_date', type: 'date', nullable: false })
  enrollmentDate: Date;

  /**
   * Date when employee started the training
   */
  @Column({ name: 'start_date', type: 'date', nullable: true })
  startDate: Date | null;

  /**
   * Date when employee completed the training
   */
  @Column({ name: 'completion_date', type: 'date', nullable: true })
  completionDate: Date | null;

  /**
   * Training status (enrolled, in_progress, completed, failed, cancelled)
   */
  @Column({ type: 'varchar', length: 50, nullable: false, default: 'enrolled' })
  status: string;

  /**
   * Employee's score/grade in the training
   */
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  score: number | null;

  /**
   * Whether employee passed the training
   */
  @Column({ type: 'boolean', nullable: true })
  passed: boolean | null;

  /**
   * Certificate number if certification was awarded
   */
  @Column({ name: 'certificate_number', type: 'varchar', length: 100, nullable: true })
  certificateNumber: string | null;

  /**
   * Certificate issue date
   */
  @Column({ name: 'certificate_issued_date', type: 'date', nullable: true })
  certificateIssuedDate: Date | null;

  /**
   * Certificate expiry date
   */
  @Column({ name: 'certificate_expiry_date', type: 'date', nullable: true })
  certificateExpiryDate: Date | null;

  /**
   * Training attendance percentage
   */
  @Column({ name: 'attendance_percentage', type: 'decimal', precision: 5, scale: 2, nullable: true })
  attendancePercentage: number | null;

  /**
   * Employee feedback on the training
   */
  @Column({ name: 'employee_feedback', type: 'text', nullable: true })
  employeeFeedback: string | null;

  /**
   * Employee rating of the training (1-5 scale)
   */
  @Column({ name: 'employee_rating', type: 'decimal', precision: 3, scale: 2, nullable: true })
  employeeRating: number | null;

  /**
   * Instructor feedback on employee performance
   */
  @Column({ name: 'instructor_feedback', type: 'text', nullable: true })
  instructorFeedback: string | null;

  /**
   * Training cost for this employee
   */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  cost: number | null;

  /**
   * Currency for the cost
   */
  @Column({ type: 'varchar', length: 10, default: 'VND' })
  currency: string;

  /**
   * Whether training was mandatory for the employee
   */
  @Column({ name: 'is_mandatory', type: 'boolean', default: false })
  isMandatory: boolean;

  /**
   * Training deadline (for mandatory trainings)
   */
  @Column({ type: 'date', nullable: true })
  deadline: Date | null;

  /**
   * Number of training hours completed
   */
  @Column({ name: 'hours_completed', type: 'decimal', precision: 5, scale: 2, nullable: true })
  hoursCompleted: number | null;

  /**
   * Training materials accessed by employee
   */
  @Column({ name: 'materials_accessed', type: 'text', nullable: true })
  materialsAccessed: string | null;

  /**
   * Additional notes about the training
   */
  @Column({ type: 'text', nullable: true })
  notes: string | null;

  /**
   * ID of the manager who approved the training
   */
  @Column({ name: 'approved_by', type: 'integer', nullable: true })
  approvedBy: number | null;

  /**
   * Date when training was approved
   */
  @Column({ name: 'approved_at', type: 'bigint', nullable: true })
  approvedAt: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: false })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   * REQUIRED for tenant isolation
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
